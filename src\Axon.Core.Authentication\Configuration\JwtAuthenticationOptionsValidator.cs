using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Axon.Core.Authentication.Configuration;

public class JwtAuthenticationOptionsValidator : IValidateOptions<JwtAuthenticationOptions>
{
    public ValidateOptionsResult Validate(string? name, JwtAuthenticationOptions options)
    {
        var failures = new List<string>();

        // Issuer validation (always required)
        if (string.IsNullOrWhiteSpace(options.Issuer) && string.IsNullOrWhiteSpace(options.ValidIssuers))
        {
            failures.Add("Issuer or ValidIssuers must be configured.");
        }
        else if (!string.IsNullOrWhiteSpace(options.Issuer) && !IsValidUri(options.Issuer))
        {
            failures.Add($"Issuer '{options.Issuer}' is not a valid URI.");
        }
        else if (!string.IsNullOrWhiteSpace(options.ValidIssuers))
        {
            var issuers = options.ValidIssuers.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var issuer in issuers)
            {
                if (!IsValidUri(issuer.Trim()))
                {
                    failures.Add($"Valid issuer '{issuer.Trim()}' is not a valid URI.");
                }
            }
        }

        // Audience validation (always required)
        if (string.IsNullOrWhiteSpace(options.Audience) && string.IsNullOrWhiteSpace(options.ValidAudiences))
        {
            failures.Add("Audience or ValidAudiences must be configured.");
        }

        // Signing key validation (always required)
        var hasPublicKeyUrl = !string.IsNullOrWhiteSpace(options.PublicKeyUrl);
        var hasPublicKey = !string.IsNullOrWhiteSpace(options.PublicKey);
        var hasSymmetricKey = !string.IsNullOrWhiteSpace(options.SymmetricKey);

        if (!hasPublicKeyUrl && !hasPublicKey && !hasSymmetricKey)
        {
            failures.Add("At least one signing key source must be configured: PublicKeyUrl, PublicKey, or SymmetricKey.");
        }

        if (hasPublicKeyUrl && !IsValidUri(options.PublicKeyUrl))
        {
            failures.Add($"PublicKeyUrl '{options.PublicKeyUrl}' is not a valid URI.");
        }

        if (hasPublicKeyUrl && options.RequireHttpsMetadata && !options.PublicKeyUrl.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
        {
            failures.Add("PublicKeyUrl must use HTTPS when RequireHttpsMetadata is true.");
        }
        if (options.ClockSkew < TimeSpan.Zero)
        {
            failures.Add("ClockSkew must be a positive time span.");
        }

        if (options.ClockSkew > TimeSpan.FromHours(1))
        {
            failures.Add("ClockSkew should not exceed 1 hour for security reasons.");
        }

        if (options.PublicKeyCacheDuration <= TimeSpan.Zero)
        {
            failures.Add("PublicKeyCacheDuration must be a positive time span.");
        }

        if (options.HttpTimeout <= TimeSpan.Zero)
        {
            failures.Add("HttpTimeout must be a positive time span.");
        }

        if (options.HttpTimeout > TimeSpan.FromMinutes(5))
        {
            failures.Add("HttpTimeout should not exceed 5 minutes to avoid blocking operations.");
        }

        if (options.AccessTokenExpirationMinutes <= TimeSpan.Zero)
        {
            failures.Add("AccessTokenExpirationMinutes must be a positive time span.");
        }

        if (options.RefreshTokenExpirationDays <= TimeSpan.Zero)
        {
            failures.Add("RefreshTokenExpirationDays must be a positive time span.");
        }
        if (string.IsNullOrWhiteSpace(options.UserIdClaimType))
        {
            failures.Add("UserIdClaimType cannot be empty.");
        }

        if (string.IsNullOrWhiteSpace(options.EmailClaimType))
        {
            failures.Add("EmailClaimType cannot be empty.");
        }

        if (string.IsNullOrWhiteSpace(options.AuthenticationScheme))
        {
            failures.Add("AuthenticationScheme cannot be empty.");
        }

        return failures.Any()
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }

    private static bool IsValidUri(string uri)
    {
        return Uri.TryCreate(uri, UriKind.Absolute, out var result) &&
               (result.Scheme == Uri.UriSchemeHttp || result.Scheme == Uri.UriSchemeHttps);
    }
}
