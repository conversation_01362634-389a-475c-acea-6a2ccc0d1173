using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Axon.Core.Authentication.Configuration;

/// <summary>
/// Validator for JWT authentication options.
/// </summary>
public class JwtAuthenticationOptionsValidator : IValidateOptions<JwtAuthenticationOptions>
{
    /// <inheritdoc />
    public ValidateOptionsResult Validate(string? name, JwtAuthenticationOptions options)
    {
        var failures = new List<string>();

        // Validate required fields
        if (string.IsNullOrWhiteSpace(options.Issuer))
        {
            failures.Add("Issuer is required and cannot be empty.");
        }

        if (string.IsNullOrWhiteSpace(options.Audience))
        {
            failures.Add("Audience is required and cannot be empty.");
        }

        if (string.IsNullOrWhiteSpace(options.PublicKeyUrl))
        {
            failures.Add("PublicKeyUrl is required and cannot be empty.");
        }
        else if (!Uri.TryCreate(options.PublicKeyUrl, UriKind.Absolute, out var uri))
        {
            failures.Add("PublicKeyUrl must be a valid absolute URL.");
        }
        else if (options.RequireHttpsMetadata && uri.Scheme != "https")
        {
            failures.Add("PublicKeyUrl must use HTTPS when RequireHttpsMetadata is true.");
        }

        // Validate time spans
        if (options.ClockSkew < TimeSpan.Zero)
        {
            failures.Add("ClockSkew cannot be negative.");
        }

        if (options.PublicKeyCacheDuration <= TimeSpan.Zero)
        {
            failures.Add("PublicKeyCacheDuration must be greater than zero.");
        }

        if (options.HttpTimeout <= TimeSpan.Zero)
        {
            failures.Add("HttpTimeout must be greater than zero.");
        }

        // Validate claim types
        if (string.IsNullOrWhiteSpace(options.UserIdClaimType))
        {
            failures.Add("UserIdClaimType is required and cannot be empty.");
        }

        if (string.IsNullOrWhiteSpace(options.EmailClaimType))
        {
            failures.Add("EmailClaimType is required and cannot be empty.");
        }

        if (string.IsNullOrWhiteSpace(options.RoleClaimType))
        {
            failures.Add("RoleClaimType is required and cannot be empty.");
        }

        // Validate using data annotations
        var validationContext = new ValidationContext(options);
        var validationResults = new List<ValidationResult>();
        
        if (!Validator.TryValidateObject(options, validationContext, validationResults, true))
        {
            failures.AddRange(validationResults.Select(vr => vr.ErrorMessage ?? "Unknown validation error"));
        }

        return failures.Count > 0 
            ? ValidateOptionsResult.Fail(failures)
            : ValidateOptionsResult.Success;
    }
}
