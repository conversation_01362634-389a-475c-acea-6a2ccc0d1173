using Microsoft.IdentityModel.Tokens;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Services;

/// <summary>
/// Service for retrieving and caching JWT signing keys.
/// </summary>
public interface IJwtKeyService
{
    /// <summary>
    /// Retrieves the signing keys for JWT validation.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token for the operation.</param>
    /// <returns>A collection of security keys for JWT validation.</returns>
    Task<IEnumerable<SecurityKey>> GetSigningKeysAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears the cached signing keys, forcing a refresh on the next request.
    /// </summary>
    Task ClearCacheAsync();
}
