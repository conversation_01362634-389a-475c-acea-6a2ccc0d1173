using Microsoft.Extensions.Configuration;
using System;
using System.Globalization;

namespace Axon.Core.Authentication.Configuration;

/// <summary>
/// Provides configuration from environment variables for JWT authentication options.
/// </summary>
public static class EnvironmentVariableConfigurationProvider
{
    /// <summary>
    /// Configures JWT authentication options from environment variables.
    /// </summary>
    /// <param name="options">The options to configure</param>
    public static void ConfigureFromEnvironment(JwtAuthenticationOptions options)
    {
        // Core JWT validation parameters
        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.Issuer, out var issuer))
            options.Issuer = issuer;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.Audience, out var audience))
            options.Audience = audience;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.PublicKeyUrl, out var publicKeyUrl))
            options.PublicKeyUrl = publicKeyUrl;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.PublicKey, out var publicKey))
            options.PublicKey = publicKey;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.SymmetricKey, out var symmetricKey))
            options.SymmetricKey = symmetricKey;

        // Validation flags
        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ValidateIssuer, out var validateIssuer))
            options.ValidateIssuer = validateIssuer;

        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ValidateAudience, out var validateAudience))
            options.ValidateAudience = validateAudience;

        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ValidateLifetime, out var validateLifetime))
            options.ValidateLifetime = validateLifetime;

        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ValidateSigningKey, out var validateSigningKey))
            options.ValidateIssuerSigningKey = validateSigningKey;

        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.RequireHttpsMetadata, out var requireHttps))
            options.RequireHttpsMetadata = requireHttps;

        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.SaveToken, out var saveToken))
            options.SaveToken = saveToken;

        // Timing configuration
        if (TryGetIntegerEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ClockSkewMinutes, out var clockSkewMinutes))
            options.ClockSkew = TimeSpan.FromMinutes(clockSkewMinutes);

        if (TryGetIntegerEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.CacheDurationHours, out var cacheDurationHours))
            options.PublicKeyCacheDuration = TimeSpan.FromHours(cacheDurationHours);

        if (TryGetIntegerEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.HttpTimeoutSeconds, out var httpTimeoutSeconds))
            options.HttpTimeout = TimeSpan.FromSeconds(httpTimeoutSeconds);

        if (TryGetIntegerEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.AccessTokenMinutes, out var accessTokenMinutes))
            options.AccessTokenExpirationMinutes = TimeSpan.FromMinutes(accessTokenMinutes);

        if (TryGetIntegerEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.RefreshTokenDays, out var refreshTokenDays))
            options.RefreshTokenExpirationDays = TimeSpan.FromDays(refreshTokenDays);

        // Claim configuration
        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.UserIdClaim, out var userIdClaim))
            options.UserIdClaimType = userIdClaim;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.EmailClaim, out var emailClaim))
            options.EmailClaimType = emailClaim;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.RoleClaim, out var roleClaim))
            options.RoleClaimType = roleClaim;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.NameClaim, out var nameClaim))
            options.NameClaimType = nameClaim;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.GroupsClaim, out var groupsClaim))
            options.GroupsClaimType = groupsClaim;

        // Advanced configuration
        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ValidIssuers, out var validIssuers))
            options.ValidIssuers = validIssuers;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.ValidAudiences, out var validAudiences))
            options.ValidAudiences = validAudiences;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.Algorithm, out var algorithm))
            options.Algorithm = algorithm;

        if (TryGetBooleanEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.IncludeErrorDetails, out var includeErrorDetails))
            options.IncludeErrorDetails = includeErrorDetails;

        if (TryGetEnvironmentVariable(JwtAuthenticationOptions.EnvironmentVariables.AuthenticationScheme, out var authScheme))
            options.AuthenticationScheme = authScheme;
    }

    /// <summary>
    /// Creates a configuration builder that includes environment variables with the AXON_AUTH_ prefix.
    /// </summary>
    /// <returns>Configuration builder with environment variables</returns>
    public static IConfigurationBuilder AddAxonAuthenticationEnvironmentVariables(this IConfigurationBuilder builder)
    {
        return builder.AddEnvironmentVariables("AXON_AUTH_");
    }

    private static bool TryGetEnvironmentVariable(string key, out string value)
    {
        value = Environment.GetEnvironmentVariable(key) ?? string.Empty;
        return !string.IsNullOrWhiteSpace(value);
    }

    private static bool TryGetBooleanEnvironmentVariable(string key, out bool value)
    {
        value = false;
        var stringValue = Environment.GetEnvironmentVariable(key);
        
        if (string.IsNullOrWhiteSpace(stringValue))
            return false;

        return bool.TryParse(stringValue, out value) ||
               (stringValue.Equals("1", StringComparison.OrdinalIgnoreCase) && (value = true)) ||
               (stringValue.Equals("0", StringComparison.OrdinalIgnoreCase) && (value = false));
    }

    private static bool TryGetIntegerEnvironmentVariable(string key, out int value)
    {
        value = 0;
        var stringValue = Environment.GetEnvironmentVariable(key);
        
        if (string.IsNullOrWhiteSpace(stringValue))
            return false;

        return int.TryParse(stringValue, NumberStyles.Integer, CultureInfo.InvariantCulture, out value);
    }
}
