using System;
using Axon.Core.Authentication.Events;
using Axon.Core.Authentication.Interfaces;
using Axon.Core.Authentication.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Core.Authentication.Configuration;

/// <summary>
/// Configures JWT Bearer options with dependency injection support.
/// </summary>
public class JwtBearerOptionsSetup : IConfigureNamedOptions<JwtBearerOptions>
{
    private readonly IOptions<JwtAuthenticationOptions> jwtOptions;
    private readonly ILogger<AxonJwtBearerEvents> logger;
    private readonly IAuthCallback? authCallback;
    private readonly IJwtKeyService jwtKeyService;

    public JwtBearerOptionsSetup(
        IOptions<JwtAuthenticationOptions> jwtOptions,
        ILogger<AxonJwtBearerEvents> logger,
        IJwtKeyService jwtKeyService,
        IAuthCallback? authCallback = null)
    {
        this.jwtOptions = jwtOptions ?? throw new ArgumentNullException(nameof(jwtOptions));
        this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        this.jwtKeyService = jwtKeyService ?? throw new ArgumentNullException(nameof(jwtKeyService));
        this.authCallback = authCallback;
    }

    public void Configure(string? name, JwtBearerOptions options)
    {
        if (name == JwtBearerAuthenticationDefaults.AuthenticationScheme)
        {
            Configure(options);
        }
    }

    public void Configure(JwtBearerOptions options)
    {
        var jwtConfig = jwtOptions.Value;

        // Configure token validation parameters
        options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
        {
            ValidateIssuer = jwtConfig.ValidateIssuer,
            ValidateAudience = jwtConfig.ValidateAudience,
            ValidateLifetime = jwtConfig.ValidateLifetime,
            ValidateIssuerSigningKey = jwtConfig.ValidateIssuerSigningKey,
            ValidIssuer = jwtConfig.Issuer,
            ValidAudience = jwtConfig.Audience,
            ClockSkew = jwtConfig.ClockSkew,
            RequireSignedTokens = true,
            RequireExpirationTime = jwtConfig.ValidateLifetime,
            NameClaimType = jwtConfig.UserIdClaimType,
            RoleClaimType = jwtConfig.RoleClaimType
        };

        // Configure metadata and security
        options.RequireHttpsMetadata = jwtConfig.RequireHttpsMetadata;
        options.SaveToken = true;
        options.IncludeErrorDetails = true;

        // Set custom events
        options.Events = new AxonJwtBearerEvents(logger, jwtOptions, authCallback);

        // Configure signing key resolver
        options.TokenValidationParameters.IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
        {
            try
            {
                // If issuer signing key validation is disabled, provide a test key for development/testing
                if (!jwtConfig.ValidateIssuerSigningKey)
                {
                    // Use a test HMAC key - this should match the key used in tests
                    var testKeyBytes = System.Text.Encoding.UTF8.GetBytes("test-secret-key-for-jwt-authentication-testing-purposes-only");
                    return new[] { new Microsoft.IdentityModel.Tokens.SymmetricSecurityKey(testKeyBytes) };
                }

                return jwtKeyService.GetSigningKeysAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to resolve signing keys for token validation");
                return new Microsoft.IdentityModel.Tokens.SecurityKey[0];
            }
        };
    }
}
