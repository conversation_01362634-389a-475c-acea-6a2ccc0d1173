using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Shouldly;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Xunit;

namespace Axon.Core.Authentication.Tests.Integration;

public class JwtAuthenticationIntegrationTests : IDisposable
{
    private readonly TestServer testServer;
    private readonly HttpClient httpClient;
    private readonly string testIssuer = "https://test.auth.com";
    private readonly string testAudience = "test-api";
    private readonly SecurityKey testKey;

    public JwtAuthenticationIntegrationTests()
    {
        // Create a test signing key - must match the key used in JwtBearerOptionsSetup
        var keyBytes = Encoding.UTF8.GetBytes("test-secret-key-for-jwt-authentication-testing-purposes-only");
        testKey = new SymmetricSecurityKey(keyBytes);

        var hostBuilder = new HostBuilder()
            .ConfigureWebHost(webHost =>
            {
                webHost.UseTestServer();
                webHost.ConfigureServices(services =>
                {
                    var configuration = new ConfigurationBuilder()
                        .AddInMemoryCollection(new Dictionary<string, string?>
                        {
                            ["AxonCoreAuthentication:Issuer"] = testIssuer,
                            ["AxonCoreAuthentication:Audience"] = testAudience,
                            ["AxonCoreAuthentication:PublicKeyUrl"] = "https://test.auth.com/.well-known/jwks.json",
                            ["AxonCoreAuthentication:ValidateIssuerSigningKey"] = "false" // Disable for test
                        })
                        .Build();

                    services.AddAxonAuthentication(configuration, options =>
                    {
                        // Override for testing - in real scenarios, keys would be fetched from the URL
                        options.ValidateIssuerSigningKey = false;
                    });

                    services.AddAuthorization();
                    services.AddRouting();
                });

                webHost.Configure(app =>
                {
                    app.UseAuthentication();
                    app.UseAuthorization();

                    app.Map("/public", publicApp =>
                    {
                        publicApp.Run(async context =>
                        {
                            if (!context.Response.HasStarted)
                            {
                                context.Response.StatusCode = 200;
                                context.Response.ContentType = "text/plain";
                            }
                            await context.Response.WriteAsync("Public endpoint");
                        });
                    });

                    app.Map("/protected", protectedApp =>
                    {
                        protectedApp.Run(async context =>
                        {
                            var result = await context.AuthenticateAsync(JwtBearerAuthenticationDefaults.AuthenticationScheme);
                            if (result.Succeeded)
                            {
                                // Try different claim types to find the user ID
                                var userId = context.User.FindFirst("sub")?.Value
                                    ?? context.User.FindFirst(System.Security.Claims.ClaimTypes.Name)?.Value
                                    ?? context.User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value
                                    ?? "unknown";

                                if (!context.Response.HasStarted)
                                {
                                    context.Response.StatusCode = 200;
                                    context.Response.ContentType = "text/plain";
                                }
                                await context.Response.WriteAsync($"Protected endpoint - User: {userId}");
                            }
                            else
                            {
                                if (!context.Response.HasStarted)
                                {
                                    context.Response.StatusCode = 401;
                                    context.Response.ContentType = "text/plain";
                                }
                                await context.Response.WriteAsync("Unauthorized");
                            }
                        });
                    });
                });
            });

        var host = hostBuilder.Build();
        host.Start();

        testServer = host.GetTestServer();
        httpClient = testServer.CreateClient();
    }

    [Fact]
    public async Task PublicEndpoint_ShouldBeAccessibleWithoutToken()
    {
        // Act
        var response = await httpClient.GetAsync("/public");

        // Assert
        response.IsSuccessStatusCode.ShouldBeTrue();
        var content = await response.Content.ReadAsStringAsync();
        content.ShouldBe("Public endpoint");
    }

    [Fact]
    public async Task ProtectedEndpoint_WithoutToken_ShouldReturnUnauthorized()
    {
        // Act
        var response = await httpClient.GetAsync("/protected");

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task ProtectedEndpoint_WithInvalidToken_ShouldReturnUnauthorized()
    {
        // Arrange
        httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "invalid-token");

        // Act
        var response = await httpClient.GetAsync("/protected");

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task ProtectedEndpoint_WithValidToken_ShouldReturnSuccess()
    {
        // Arrange
        var token = CreateTestToken("test-user-123");
        httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await httpClient.GetAsync("/protected");

        // Assert
        var content = await response.Content.ReadAsStringAsync();
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.OK, $"Expected 200 OK but got {response.StatusCode}. Content: {content}");
        content.ShouldBe("Protected endpoint - User: test-user-123");
    }

    [Fact]
    public async Task ProtectedEndpoint_WithExpiredToken_ShouldReturnUnauthorized()
    {
        // Arrange
        var token = CreateTestToken("test-user-123", DateTime.UtcNow.AddMinutes(-10)); // Expired token
        httpClient.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await httpClient.GetAsync("/protected");

        // Assert
        response.StatusCode.ShouldBe(System.Net.HttpStatusCode.Unauthorized);
    }

    private string CreateTestToken(string userId, DateTime? expiry = null)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var now = DateTime.UtcNow;
        var expiryTime = expiry ?? now.AddHours(1);

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim("sub", userId),
                new Claim("email", $"{userId}@test.com"),
                new Claim("roles", "user")
            }),
            NotBefore = expiry.HasValue && expiry < now ? expiry.Value.AddMinutes(-5) : now.AddMinutes(-1),
            Expires = expiryTime,
            Issuer = testIssuer,
            Audience = testAudience,
            SigningCredentials = new SigningCredentials(testKey, SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }

    public void Dispose()
    {
        httpClient?.Dispose();
        testServer?.Dispose();
    }
}
