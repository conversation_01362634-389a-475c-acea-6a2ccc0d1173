using Axon.Core.Authentication.Configuration;
using Microsoft.Extensions.Options;
using Shouldly;
using System;
using Xunit;

namespace Axon.Core.Authentication.Tests.Configuration;

public class JwtAuthenticationOptionsValidatorTests
{
    private readonly JwtAuthenticationOptionsValidator validator;

    public JwtAuthenticationOptionsValidatorTests()
    {
        validator = new JwtAuthenticationOptionsValidator();
    }

    [Fact]
    public void Validate_WithValidOptions_ShouldReturnSuccess()
    {
        // Arrange
        var options = new JwtAuthenticationOptions
        {
            Issuer = "https://auth.example.com",
            Audience = "test-api",
            PublicKeyUrl = "https://auth.example.com/.well-known/jwks.json",
            ClockSkew = TimeSpan.FromMinutes(5),
            PublicKeyCacheDuration = TimeSpan.FromHours(1),
            HttpTimeout = TimeSpan.FromSeconds(30),
            UserIdClaimType = "sub",
            EmailClaimType = "email",
            RoleClaimType = "roles"
        };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.ShouldBe(ValidateOptionsResult.Success);
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Validate_WithInvalidIssuer_ShouldReturnFailure(string? issuer)
    {
        // Arrange
        var options = new JwtAuthenticationOptions { Issuer = issuer! };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Issuer is required and cannot be empty.");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Validate_WithInvalidAudience_ShouldReturnFailure(string? audience)
    {
        // Arrange
        var options = new JwtAuthenticationOptions { Audience = audience! };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("Audience is required and cannot be empty.");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Validate_WithInvalidPublicKeyUrl_ShouldReturnFailure(string? publicKeyUrl)
    {
        // Arrange
        var options = new JwtAuthenticationOptions { PublicKeyUrl = publicKeyUrl! };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("PublicKeyUrl is required and cannot be empty.");
    }

    [Theory]
    [InlineData("not-a-url")]
    [InlineData("relative/path")]
    public void Validate_WithInvalidPublicKeyUrlFormat_ShouldReturnFailure(string publicKeyUrl)
    {
        // Arrange
        var options = new JwtAuthenticationOptions { PublicKeyUrl = publicKeyUrl };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("PublicKeyUrl must be a valid absolute URL.");
    }

    [Fact]
    public void Validate_WithNonHttpsUrl_ShouldReturnFailure()
    {
        // Arrange
        var options = new JwtAuthenticationOptions { PublicKeyUrl = "ftp://invalid.com" };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("PublicKeyUrl must use HTTPS when RequireHttpsMetadata is true.");
    }

    [Fact]
    public void Validate_WithHttpUrlWhenHttpsRequired_ShouldReturnFailure()
    {
        // Arrange
        var options = new JwtAuthenticationOptions 
        { 
            PublicKeyUrl = "http://auth.example.com/keys",
            RequireHttpsMetadata = true
        };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("PublicKeyUrl must use HTTPS when RequireHttpsMetadata is true.");
    }

    [Fact]
    public void Validate_WithHttpUrlWhenHttpsNotRequired_ShouldReturnSuccess()
    {
        // Arrange
        var options = new JwtAuthenticationOptions 
        { 
            PublicKeyUrl = "http://auth.example.com/keys",
            RequireHttpsMetadata = false
        };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Succeeded.ShouldBeTrue();
    }

    [Fact]
    public void Validate_WithNegativeClockSkew_ShouldReturnFailure()
    {
        // Arrange
        var options = new JwtAuthenticationOptions { ClockSkew = TimeSpan.FromMinutes(-1) };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("ClockSkew cannot be negative.");
    }

    [Fact]
    public void Validate_WithZeroPublicKeyCacheDuration_ShouldReturnFailure()
    {
        // Arrange
        var options = new JwtAuthenticationOptions { PublicKeyCacheDuration = TimeSpan.Zero };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("PublicKeyCacheDuration must be greater than zero.");
    }

    [Fact]
    public void Validate_WithZeroHttpTimeout_ShouldReturnFailure()
    {
        // Arrange
        var options = new JwtAuthenticationOptions { HttpTimeout = TimeSpan.Zero };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("HttpTimeout must be greater than zero.");
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void Validate_WithInvalidClaimTypes_ShouldReturnFailure(string? claimType)
    {
        // Arrange
        var options = new JwtAuthenticationOptions 
        { 
            UserIdClaimType = claimType!,
            EmailClaimType = claimType!,
            RoleClaimType = claimType!
        };

        // Act
        var result = validator.Validate(null, options);

        // Assert
        result.Failed.ShouldBeTrue();
        result.Failures.ShouldContain("UserIdClaimType is required and cannot be empty.");
        result.Failures.ShouldContain("EmailClaimType is required and cannot be empty.");
        result.Failures.ShouldContain("RoleClaimType is required and cannot be empty.");
    }
}
