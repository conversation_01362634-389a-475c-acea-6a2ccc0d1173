using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Axon.Core.Authentication.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;

namespace Axon.Core.Authentication.Services;

/// <summary>
/// Service for retrieving and caching JWT signing keys from a JWKS endpoint.
/// </summary>
public class JwtKeyService : IJwtKeyService, IDisposable
{
    private readonly JwtAuthenticationOptions options;
    private readonly HttpClient httpClient;
    private readonly ILogger<JwtKeyService> logger;
    private readonly SemaphoreSlim semaphore;
    
    private IEnumerable<SecurityKey>? cachedKeys;
    private DateTime? cacheExpiry;
    private bool disposed;

    public JwtKeyService(
        IOptions<JwtAuthenticationOptions> options,
        HttpClient httpClient,
        ILogger<JwtKeyService> logger)
    {
        this.options = options.Value ?? throw new ArgumentNullException(nameof(options));
        this.httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        this.semaphore = new SemaphoreSlim(1, 1);

        // Configure HttpClient timeout
        this.httpClient.Timeout = this.options.HttpTimeout;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<SecurityKey>> GetSigningKeysAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        // Check if we have valid cached keys
        if (cachedKeys != null && cacheExpiry.HasValue && DateTime.UtcNow < cacheExpiry.Value)
        {
            logger.LogDebug("Returning cached JWT signing keys");
            return cachedKeys;
        }

        await semaphore.WaitAsync(cancellationToken);
        try
        {
            // Double-check after acquiring the lock
            if (cachedKeys != null && cacheExpiry.HasValue && DateTime.UtcNow < cacheExpiry.Value)
            {
                logger.LogDebug("Returning cached JWT signing keys (double-check)");
                return cachedKeys;
            }

            logger.LogInformation("Fetching JWT signing keys from {PublicKeyUrl}", options.PublicKeyUrl);
            
            var keys = await FetchSigningKeysAsync(cancellationToken);
            
            // Cache the keys
            cachedKeys = keys;
            cacheExpiry = DateTime.UtcNow.Add(options.PublicKeyCacheDuration);
            
            logger.LogInformation("Successfully cached {KeyCount} JWT signing keys", keys.Count());
            return keys;
        }
        finally
        {
            semaphore.Release();
        }
    }

    /// <inheritdoc />
    public async Task ClearCacheAsync()
    {
        ThrowIfDisposed();

        await semaphore.WaitAsync();
        try
        {
            cachedKeys = null;
            cacheExpiry = null;
            logger.LogInformation("JWT signing key cache cleared");
        }
        finally
        {
            semaphore.Release();
        }
    }

    private async Task<IEnumerable<SecurityKey>> FetchSigningKeysAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Use OpenID Connect configuration manager for standard JWKS endpoints
            if (options.PublicKeyUrl.EndsWith("/.well-known/openid_configuration") || 
                options.PublicKeyUrl.Contains("/.well-known/"))
            {
                return await FetchKeysFromOpenIdConfigurationAsync(cancellationToken);
            }
            
            // Direct JWKS endpoint
            return await FetchKeysFromJwksAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to fetch JWT signing keys from {PublicKeyUrl}", options.PublicKeyUrl);
            throw new InvalidOperationException($"Failed to retrieve JWT signing keys: {ex.Message}", ex);
        }
    }

    private async Task<IEnumerable<SecurityKey>> FetchKeysFromOpenIdConfigurationAsync(CancellationToken cancellationToken)
    {
        var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
            options.PublicKeyUrl,
            new OpenIdConnectConfigurationRetriever(),
            new HttpDocumentRetriever(httpClient)
            {
                RequireHttps = options.RequireHttpsMetadata
            });

        var configuration = await configurationManager.GetConfigurationAsync(cancellationToken);
        return configuration.SigningKeys;
    }

    private async Task<IEnumerable<SecurityKey>> FetchKeysFromJwksAsync(CancellationToken cancellationToken)
    {
        var response = await httpClient.GetAsync(options.PublicKeyUrl, cancellationToken);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        var jwks = JsonSerializer.Deserialize<JsonWebKeySet>(content);

        if (jwks?.Keys == null || !jwks.Keys.Any())
        {
            throw new InvalidOperationException("No keys found in JWKS response");
        }

        return jwks.Keys.Select(key => (SecurityKey)key);
    }

    private void ThrowIfDisposed()
    {
        if (disposed)
        {
            throw new ObjectDisposedException(nameof(JwtKeyService));
        }
    }

    public void Dispose()
    {
        if (!disposed)
        {
            semaphore?.Dispose();
            disposed = true;
        }
    }
}
