using System;
using Axon.Core.Authentication.Configuration;
using Shouldly;
using Xunit;

namespace Axon.Core.Authentication.Tests.Configuration;

public class JwtAuthenticationOptionsTests
{
    [Fact]
    public void Constructor_ShouldSetDefaultValues()
    {
        // Arrange & Act
        var options = new JwtAuthenticationOptions();

        // Assert
        options.Issuer.ShouldBe("https://auth.smartphlex.com");
        options.Audience.ShouldBe("smartphlex-api");
        options.PublicKeyUrl.ShouldBe("https://auth.smartphlex.com/.well-known/jwks.json");
        options.ValidateIssuer.ShouldBeTrue();
        options.ValidateAudience.ShouldBeTrue();
        options.ValidateLifetime.ShouldBeTrue();
        options.ValidateIssuerSigningKey.ShouldBeTrue();
        options.ClockSkew.ShouldBe(TimeSpan.FromMinutes(5));
        options.PublicKeyCacheDuration.ShouldBe(TimeSpan.FromHours(24));
        options.HttpTimeout.ShouldBe(TimeSpan.FromSeconds(30));
        options.RequireHttpsMetadata.ShouldBeTrue();
        options.UserIdClaimType.ShouldBe("sub");
        options.EmailClaimType.ShouldBe("email");
        options.RoleClaimType.ShouldBe("roles");
    }

    [Fact]
    public void SectionName_ShouldBeCorrect()
    {
        // Assert
        JwtAuthenticationOptions.SECTION_NAME.ShouldBe("AxonCoreAuthentication");
    }

    [Theory]
    [InlineData("https://custom.auth.com", "custom-api", "https://custom.auth.com/keys")]
    [InlineData("https://another.auth.com", "another-api", "https://another.auth.com/.well-known/jwks.json")]
    public void Properties_ShouldBeSettable(string issuer, string audience, string publicKeyUrl)
    {
        // Arrange
        var options = new JwtAuthenticationOptions();

        // Act
        options.Issuer = issuer;
        options.Audience = audience;
        options.PublicKeyUrl = publicKeyUrl;

        // Assert
        options.Issuer.ShouldBe(issuer);
        options.Audience.ShouldBe(audience);
        options.PublicKeyUrl.ShouldBe(publicKeyUrl);
    }

    [Theory]
    [InlineData(true, false, true, false)]
    [InlineData(false, true, false, true)]
    public void ValidationFlags_ShouldBeSettable(bool validateIssuer, bool validateAudience, bool validateLifetime, bool validateSigningKey)
    {
        // Arrange
        var options = new JwtAuthenticationOptions();

        // Act
        options.ValidateIssuer = validateIssuer;
        options.ValidateAudience = validateAudience;
        options.ValidateLifetime = validateLifetime;
        options.ValidateIssuerSigningKey = validateSigningKey;

        // Assert
        options.ValidateIssuer.ShouldBe(validateIssuer);
        options.ValidateAudience.ShouldBe(validateAudience);
        options.ValidateLifetime.ShouldBe(validateLifetime);
        options.ValidateIssuerSigningKey.ShouldBe(validateSigningKey);
    }

    [Theory]
    [InlineData(1, 2, 3)]
    [InlineData(10, 48, 60)]
    public void TimeSpanProperties_ShouldBeSettable(int clockSkewMinutes, int cacheDurationHours, int httpTimeoutSeconds)
    {
        // Arrange
        var options = new JwtAuthenticationOptions();

        // Act
        options.ClockSkew = TimeSpan.FromMinutes(clockSkewMinutes);
        options.PublicKeyCacheDuration = TimeSpan.FromHours(cacheDurationHours);
        options.HttpTimeout = TimeSpan.FromSeconds(httpTimeoutSeconds);

        // Assert
        options.ClockSkew.ShouldBe(TimeSpan.FromMinutes(clockSkewMinutes));
        options.PublicKeyCacheDuration.ShouldBe(TimeSpan.FromHours(cacheDurationHours));
        options.HttpTimeout.ShouldBe(TimeSpan.FromSeconds(httpTimeoutSeconds));
    }

    [Theory]
    [InlineData("custom_sub", "custom_email", "custom_roles")]
    [InlineData("user_id", "user_email", "user_roles")]
    public void ClaimTypes_ShouldBeSettable(string userIdClaim, string emailClaim, string roleClaim)
    {
        // Arrange
        var options = new JwtAuthenticationOptions();

        // Act
        options.UserIdClaimType = userIdClaim;
        options.EmailClaimType = emailClaim;
        options.RoleClaimType = roleClaim;

        // Assert
        options.UserIdClaimType.ShouldBe(userIdClaim);
        options.EmailClaimType.ShouldBe(emailClaim);
        options.RoleClaimType.ShouldBe(roleClaim);
    }
}
