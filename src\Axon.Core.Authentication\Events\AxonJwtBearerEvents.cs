﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Events;

public class AxonJwtBearerEvents(ILogger<AxonJwtBearerEvents> logger) : JwtBearerEvents
{
    public override async Task TokenValidated(TokenValidatedContext context)
    {
        var claims = context.Principal?.Claims.ToList() ?? [];

        // get all emails from claims here and look up user in the database here

        // if user is found, add claims 
        ClaimsIdentity ci = context.Principal.Identity as ClaimsIdentity;

        await base.TokenValidated(context);
    }

    public override async Task AuthenticationFailed(AuthenticationFailedContext context)
    {
        if (!context.HttpContext.User.Identity.IsAuthenticated && context.Scheme.Name.Equals(JwtBearerAuthenticationDefaults.AuthenticationScheme))
        {
            var claims = context.Principal?.Claims.ToList() ?? [];

        }

        await base.AuthenticationFailed(context);
    }
}
