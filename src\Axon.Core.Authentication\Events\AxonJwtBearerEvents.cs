﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Events;

public class AxonJwtBearerEvents : JwtBearerEvents
{
    private readonly ILogger<AxonJwtBearerEvents> logger;
    private readonly JwtAuthenticationOptions options;
    private readonly IAuthCallback? authCallback;

    public AxonJwtBearerEvents(
        ILogger<AxonJwtBearerEvents> logger,
        IOptions<JwtAuthenticationOptions> options,
        IAuthCallback? authCallback = null)
    {
        this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        this.options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        this.authCallback = authCallback;
    }

    public override async Task TokenValidated(TokenValidatedContext context)
    {
        try
        {
            var claims = context.Principal?.Claims.ToList() ?? [];
            var userId = GetClaimValue(claims, options.UserIdClaimType);
            var email = GetClaimValue(claims, options.EmailClaimType);

            logger.LogInformation("JWT token validated successfully for user {UserId} with email {Email}",
                userId, email);

            if (context.Principal?.Identity is ClaimsIdentity claimsIdentity)
            {
                await AddCustomClaimsAsync(claimsIdentity, claims);
            }

            if (authCallback != null)
            {
                await authCallback.OnAuthReceived(context);
            }

            await base.TokenValidated(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during token validation");
            context.Fail("Token validation failed due to an internal error");
        }
    }

    /// <inheritdoc />
    public override async Task AuthenticationFailed(AuthenticationFailedContext context)
    {
        var errorMessage = context.Exception?.Message ?? "Unknown authentication error";

        logger.LogWarning("JWT authentication failed: {ErrorMessage}", errorMessage);

        if (logger.IsEnabled(LogLevel.Debug))
        {
            logger.LogDebug("Authentication failure details: {Exception}", context.Exception);
        }

        context.Response.StatusCode = 401;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "unauthorized",
            message = "Invalid or expired token"
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));

        await base.AuthenticationFailed(context);
    }

    private static string? GetClaimValue(IEnumerable<Claim> claims, string claimType)
    {
        return claims.FirstOrDefault(c => c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase))?.Value;
    }

    private async Task AddCustomClaimsAsync(ClaimsIdentity claimsIdentity, IList<Claim> existingClaims)
    {
        await Task.CompletedTask;
    }
}
