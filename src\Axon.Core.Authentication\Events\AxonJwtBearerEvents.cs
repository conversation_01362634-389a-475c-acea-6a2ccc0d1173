﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Events;

public class AxonJwtBearerEvents(ILogger<AxonJwtBearerEvents> logger, JwtAuthenticationOptions options) : JwtBearerEvents
{
    public override async Task TokenValidated(TokenValidatedContext context)
    {
        try
        {
            var claims = context.Principal?.Claims.ToList() ?? [];
            var userId = GetClaimValue(claims, options.UserIdClaimType);
            var email = GetClaimValue(claims, options.EmailClaimType);

            if (string.IsNullOrWhiteSpace(userId))
            {
                context.Fail($"Missing required claim: {options.UserIdClaimType}");
                return;
            }

            logger.LogInformation("JWT token validated successfully for user {UserId} with email {Email}",
                userId, email);

            if (context.Principal?.Identity is ClaimsIdentity claimsIdentity)
            {
                await AddCustomClaimsAsync(claimsIdentity, claims);
            }

            await base.TokenValidated(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during token validation");
            context.Fail("Token validation failed due to an internal error");
        }
    }

    public override async Task AuthenticationFailed(AuthenticationFailedContext context)
    {
        var errorMessage = context.Exception?.Message ?? "Unknown authentication error";

        logger.LogWarning("JWT authentication failed: {ErrorMessage}", errorMessage);

        if (logger.IsEnabled(LogLevel.Debug))
        {
            logger.LogDebug("Authentication failure details: {Exception}", context.Exception);
        }

        context.Response.StatusCode = context.Exception switch
        {
            Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException => 400,
            _ => 401
        };
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "unauthorized",
            message = "Invalid or expired token"
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));

        await base.AuthenticationFailed(context);
    }

    private static string? GetClaimValue(IEnumerable<Claim> claims, string claimType)
    {
        return claims.FirstOrDefault(c => c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase))?.Value;
    }

    private async Task AddCustomClaimsAsync(ClaimsIdentity claimsIdentity, IList<Claim> existingClaims)
    {
        await Task.CompletedTask;
    }
}
