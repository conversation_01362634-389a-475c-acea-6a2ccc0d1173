﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Axon.Core.Authentication.Events;

/// <summary>
/// Custom JWT Bearer events for Axon authentication with enhanced logging and validation.
/// </summary>
public class AxonJwtBearerEvents : JwtBearerEvents
{
    private readonly ILogger<AxonJwtBearerEvents> logger;
    private readonly JwtAuthenticationOptions options;
    private readonly IAuthCallback? authCallback;

    public AxonJwtBearerEvents(
        ILogger<AxonJwtBearerEvents> logger,
        IOptions<JwtAuthenticationOptions> options,
        IAuthCallback? authCallback = null)
    {
        this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        this.options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        this.authCallback = authCallback;
    }

    /// <inheritdoc />
    public override async Task TokenValidated(TokenValidatedContext context)
    {
        try
        {
            // Validate context and principal
            if (context?.Principal?.Identity is not ClaimsIdentity claimsIdentity)
            {
                logger.LogWarning("Token validation failed: Invalid principal or claims identity");
                context?.Fail("Invalid token principal");
                return;
            }

            var claims = context.Principal.Claims.ToList();
            var userId = GetClaimValue(claims, options.UserIdClaimType);
            var email = GetClaimValue(claims, options.EmailClaimType);
            var roles = GetClaimValues(claims, options.RoleClaimType);

            // Log successful validation with more details
            logger.LogInformation("JWT token validated successfully for user {UserId} with email {Email} and roles [{Roles}]",
                userId ?? "unknown",
                email ?? "unknown",
                string.Join(", ", roles));

            // Validate required claims
            if (string.IsNullOrEmpty(userId))
            {
                logger.LogWarning("Token validation failed: Missing required user ID claim '{ClaimType}'", options.UserIdClaimType);
                context.Fail($"Missing required claim: {options.UserIdClaimType}");
                return;
            }

            // Get all email claims for database lookup
            var emailClaims = GetEmailClaims(claims);
            if (emailClaims.Any())
            {
                logger.LogDebug("Found {EmailCount} email claims for user {UserId}: [{Emails}]",
                    emailClaims.Count, userId, string.Join(", ", emailClaims));

                // Perform database lookup and add additional claims
                await PerformDatabaseLookupAndAddClaims(claimsIdentity, userId, emailClaims);
            }
            else
            {
                logger.LogWarning("No email claims found for user {UserId}", userId);
            }

            // Add any additional custom claims
            await AddCustomClaimsAsync(claimsIdentity, claims);

            // Call custom authentication callback if provided
            if (authCallback != null)
            {
                try
                {
                    await authCallback.OnAuthReceived(context);
                    logger.LogDebug("Custom authentication callback executed successfully for user {UserId}", userId);
                }
                catch (Exception callbackEx)
                {
                    logger.LogError(callbackEx, "Custom authentication callback failed for user {UserId}", userId);
                    // Don't fail the entire authentication for callback errors, just log them
                }
            }

            await base.TokenValidated(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during token validation");
            context?.Fail("Token validation failed due to an internal error");
        }
    }

    /// <inheritdoc />
    public override async Task AuthenticationFailed(AuthenticationFailedContext context)
    {
        if (context?.Response?.HasStarted == true)
        {
            logger.LogWarning("Cannot write authentication failure response - response has already started");
            return;
        }

        var errorMessage = context?.Exception?.Message ?? "Unknown authentication error";
        var errorType = GetAuthenticationErrorType(context?.Exception);

        // Log with appropriate level based on error type
        if (IsExpectedAuthError(context?.Exception))
        {
            logger.LogInformation("JWT authentication failed: {ErrorType} - {ErrorMessage}", errorType, errorMessage);
        }
        else
        {
            logger.LogWarning("JWT authentication failed: {ErrorType} - {ErrorMessage}", errorType, errorMessage);
        }

        // Log additional details for debugging
        if (logger.IsEnabled(LogLevel.Debug) && context?.Exception != null)
        {
            logger.LogDebug("Authentication failure details: {Exception}", context.Exception);

            // Log request details for debugging
            var request = context.Request;
            logger.LogDebug("Failed request details - Method: {Method}, Path: {Path}, UserAgent: {UserAgent}",
                request?.Method, request?.Path, request?.Headers["User-Agent"].FirstOrDefault());
        }

        // Set response details
        if (context?.Response != null)
        {
            context.Response.StatusCode = GetStatusCodeForError(errorType);
            context.Response.ContentType = "application/json";
            context.Response.Headers["WWW-Authenticate"] = $"Bearer realm=\"{options.Audience}\", error=\"{errorType.ToLowerInvariant()}\", error_description=\"{GetUserFriendlyErrorMessage(errorType)}\"";

            var response = new
            {
                error = errorType.ToLowerInvariant(),
                message = GetUserFriendlyErrorMessage(errorType),
                timestamp = DateTimeOffset.UtcNow.ToString("O"),
                path = context.Request?.Path.Value
            };

            var jsonResponse = System.Text.Json.JsonSerializer.Serialize(response, new System.Text.Json.JsonSerializerOptions
            {
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            });

            await context.Response.WriteAsync(jsonResponse);
        }

        await base.AuthenticationFailed(context);
    }


    private static string? GetClaimValue(IEnumerable<Claim> claims, string claimType)
    {
        return claims.FirstOrDefault(c => c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase))?.Value;
    }

    private static List<string> GetClaimValues(IEnumerable<Claim> claims, string claimType)
    {
        return claims
            .Where(c => c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase))
            .Select(c => c.Value)
            .Where(v => !string.IsNullOrWhiteSpace(v))
            .ToList();
    }

    private static List<string> GetEmailClaims(IEnumerable<Claim> claims)
    {
        var emailClaimTypes = new[] { "email", "emails", "preferred_username", "upn", "unique_name" };
        var emails = new List<string>();

        foreach (var claimType in emailClaimTypes)
        {
            var claimValues = claims
                .Where(c => c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase))
                .Select(c => c.Value)
                .Where(v => !string.IsNullOrWhiteSpace(v) && IsValidEmail(v));

            emails.AddRange(claimValues);
        }

        return emails.Distinct(StringComparer.OrdinalIgnoreCase).ToList();
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }

    private async Task PerformDatabaseLookupAndAddClaims(ClaimsIdentity claimsIdentity, string userId, List<string> emailClaims)
    {
        try
        {
            // This is where you would implement actual database lookup
            // For now, this is a placeholder that demonstrates the pattern

            logger.LogDebug("Performing database lookup for user {UserId} with emails [{Emails}]",
                userId, string.Join(", ", emailClaims));

            // Example: Look up user in database by email claims
            // var user = await userService.FindUserByEmailsAsync(emailClaims);
            // if (user != null)
            // {
            //     // Add additional claims from database
            //     claimsIdentity.AddClaim(new Claim("database_user_id", user.Id.ToString()));
            //     claimsIdentity.AddClaim(new Claim("full_name", user.FullName));
            //     claimsIdentity.AddClaim(new Claim("department", user.Department));
            //
            //     foreach (var role in user.Roles)
            //     {
            //         claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role));
            //     }
            //
            //     logger.LogInformation("Added {ClaimCount} additional claims for user {UserId} from database",
            //         additionalClaims.Count, userId);
            // }
            // else
            // {
            //     logger.LogWarning("User {UserId} not found in database with emails [{Emails}]",
            //         userId, string.Join(", ", emailClaims));
            // }

            await Task.CompletedTask; // Placeholder for async database operations
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to perform database lookup for user {UserId}", userId);
            // Don't fail authentication for database lookup errors
        }
    }

    private async Task AddCustomClaimsAsync(ClaimsIdentity claimsIdentity, IList<Claim> existingClaims)
    {
        try
        {
            // Add any additional custom claims based on business logic
            // This method can be overridden or extended for specific business requirements

            // Example: Add timestamp claim
            claimsIdentity.AddClaim(new Claim("auth_time", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()));

            // Example: Add authentication method claim
            claimsIdentity.AddClaim(new Claim("amr", "jwt"));

            logger.LogDebug("Added custom claims to identity");
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to add custom claims");
            // Don't fail authentication for custom claims errors
        }
    }

    private static string GetAuthenticationErrorType(Exception? exception)
    {
        return exception switch
        {
            Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException => "token_expired",
            Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException => "invalid_signature",
            Microsoft.IdentityModel.Tokens.SecurityTokenInvalidIssuerException => "invalid_issuer",
            Microsoft.IdentityModel.Tokens.SecurityTokenInvalidAudienceException => "invalid_audience",
            Microsoft.IdentityModel.Tokens.SecurityTokenNotYetValidException => "token_not_yet_valid",
            Microsoft.IdentityModel.Tokens.SecurityTokenNoExpirationException => "missing_expiration",
            Microsoft.IdentityModel.Tokens.SecurityTokenInvalidLifetimeException => "invalid_lifetime",
            Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException => "malformed_token",
            Microsoft.IdentityModel.Tokens.SecurityTokenArgumentException => "invalid_token_argument",
            _ => "invalid_token"
        };
    }

    private static bool IsExpectedAuthError(Exception? exception)
    {
        // These are expected authentication failures that don't need warning-level logging
        return exception is Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException or
               Microsoft.IdentityModel.Tokens.SecurityTokenMalformedException or
               Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException;
    }

    private static int GetStatusCodeForError(string errorType)
    {
        return errorType switch
        {
            "token_expired" => 401,
            "invalid_signature" => 401,
            "invalid_issuer" => 401,
            "invalid_audience" => 401,
            "token_not_yet_valid" => 401,
            "missing_expiration" => 400,
            "invalid_lifetime" => 400,
            "malformed_token" => 400,
            "invalid_token_argument" => 400,
            _ => 401
        };
    }

    private static string GetUserFriendlyErrorMessage(string errorType)
    {
        return errorType switch
        {
            "token_expired" => "The access token has expired",
            "invalid_signature" => "The access token signature is invalid",
            "invalid_issuer" => "The access token issuer is not trusted",
            "invalid_audience" => "The access token is not intended for this audience",
            "token_not_yet_valid" => "The access token is not yet valid",
            "missing_expiration" => "The access token is missing expiration information",
            "invalid_lifetime" => "The access token has an invalid lifetime",
            "malformed_token" => "The access token is malformed",
            "invalid_token_argument" => "The access token contains invalid arguments",
            _ => "The access token is invalid or expired"
        };
    }
}
