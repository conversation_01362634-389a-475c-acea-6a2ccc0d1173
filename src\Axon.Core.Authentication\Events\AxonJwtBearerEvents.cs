using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Axon.Core.Authentication.Events;

/// <summary>
/// Custom JWT Bearer events for Axon authentication with enhanced logging and validation.
/// </summary>
public class AxonJwtBearerEvents : JwtBearerEvents
{
    private readonly ILogger<AxonJwtBearerEvents> logger;
    private readonly JwtAuthenticationOptions options;
    private readonly IAuthCallback? authCallback;

    public AxonJwtBearerEvents(
        ILogger<AxonJwtBearerEvents> logger,
        IOptions<JwtAuthenticationOptions> options,
        IAuthCallback? authCallback = null)
    {
        this.logger = logger ?? throw new ArgumentNullException(nameof(logger));
        this.options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        this.authCallback = authCallback;
    }

    /// <inheritdoc />
    public override async Task TokenValidated(TokenValidatedContext context)
    {
        try
        {
            var claims = context.Principal?.Claims.ToList() ?? [];
            var userId = GetClaimValue(claims, options.UserIdClaimType);
            var email = GetClaimValue(claims, options.EmailClaimType);

            logger.LogInformation("JWT token validated successfully for user {UserId} with email {Email}",
                userId, email);

            // Log all claims for debugging (be careful with sensitive data in production)
            if (logger.IsEnabled(LogLevel.Debug))
            {
                foreach (var claim in claims)
                {
                    logger.LogDebug("Claim: {Type} = {Value}", claim.Type, claim.Value);
                }
            }

            // Add custom claims if needed
            if (context.Principal?.Identity is ClaimsIdentity claimsIdentity)
            {
                await AddCustomClaimsAsync(claimsIdentity, claims);
            }

            // Call custom authentication callback if provided
            if (authCallback != null)
            {
                // Use the TokenValidatedContext directly since it contains all needed information
                await authCallback.OnAuthReceived(context);
            }

            await base.TokenValidated(context);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during token validation");
            context.Fail("Token validation failed due to an internal error");
        }
    }

    /// <inheritdoc />
    public override async Task AuthenticationFailed(AuthenticationFailedContext context)
    {
        var errorMessage = context.Exception?.Message ?? "Unknown authentication error";

        logger.LogWarning("JWT authentication failed: {ErrorMessage}", errorMessage);

        // Log additional details for debugging
        if (logger.IsEnabled(LogLevel.Debug))
        {
            logger.LogDebug("Authentication failure details: {Exception}", context.Exception);
        }

        // Don't expose internal error details to the client
        context.Response.StatusCode = 401;
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "unauthorized",
            message = "Invalid or expired token"
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
        context.Result = AuthenticateResult.Fail("Invalid or expired token");

        await base.AuthenticationFailed(context);
    }

    /// <inheritdoc />
    public override async Task Challenge(JwtBearerChallengeContext context)
    {
        logger.LogInformation("JWT authentication challenge issued");
        await base.Challenge(context);
    }

    /// <inheritdoc />
    public override async Task MessageReceived(MessageReceivedContext context)
    {
        // Extract token from custom headers or query parameters if needed
        var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Replace("Bearer ", "");

        if (string.IsNullOrEmpty(token))
        {
            // Try to get token from query parameter (useful for WebSocket connections)
            token = context.Request.Query["access_token"].FirstOrDefault();
            if (!string.IsNullOrEmpty(token))
            {
                context.Token = token;
                logger.LogDebug("JWT token extracted from query parameter");
            }
        }
        else
        {
            logger.LogDebug("JWT token extracted from Authorization header");
        }

        await base.MessageReceived(context);
    }

    private static string? GetClaimValue(IEnumerable<Claim> claims, string claimType)
    {
        return claims.FirstOrDefault(c => c.Type.Equals(claimType, StringComparison.OrdinalIgnoreCase))?.Value;
    }

    private async Task AddCustomClaimsAsync(ClaimsIdentity claimsIdentity, IList<Claim> existingClaims)
    {
        // Add any custom claims based on business logic
        // This is where you could look up user information from a database
        // and add additional claims to the identity

        await Task.CompletedTask; // Placeholder for async operations
    }
}
