using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Events;
using Axon.Core.Authentication.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

using Microsoft.IdentityModel.Tokens;
using NSubstitute;
using Shouldly;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;
using Xunit;

namespace Axon.Core.Authentication.Tests.Events;

public class AxonJwtBearerEventsTests
{
    private readonly ILogger<AxonJwtBearerEvents> logger;
    private readonly IAuthCallback authCallback;
    private readonly AxonJwtBearerEvents events;

    public AxonJwtBearerEventsTests()
    {
        logger = Substitute.For<ILogger<AxonJwtBearerEvents>>();
        authCallback = Substitute.For<IAuthCallback>();
        
        var jwtOptions = new JwtAuthenticationOptions
        {
            UserIdClaimType = "sub",
            EmailClaimType = "email",
            Audience = "test-audience"
        };
        
        events = new AxonJwtBearerEvents(logger, jwtOptions, authCallback);
    }

    [Fact]
    public async Task TokenValidated_WithValidContext_ShouldSucceed()
    {
        // Arrange
        var context = CreateTokenValidatedContext();

        // Act
        await events.TokenValidated(context);

        // Assert
        context.Principal.ShouldNotBeNull();
        context.Principal.Identity.ShouldNotBeNull();
        context.Principal.Identity.IsAuthenticated.ShouldBeTrue();
    }

    [Fact]
    public async Task TokenValidated_WithMissingUserId_ShouldFail()
    {
        // Arrange
        var context = CreateTokenValidatedContext(includeUserId: false);

        // Act
        await events.TokenValidated(context);

        // Assert
        context.Result.ShouldNotBeNull();
        context.Result.Succeeded.ShouldBeFalse();
        context.Result.Failure?.Message.ShouldContain("Missing required claim: sub");
    }

    [Fact]
    public async Task AuthenticationFailed_WithExpiredToken_ShouldReturnCorrectError()
    {
        // Arrange
        var context = CreateAuthenticationFailedContext(new SecurityTokenExpiredException("Token expired"));
        
        // Act
        await events.AuthenticationFailed(context);
        
        // Assert
        context.Response.StatusCode.ShouldBe(401);
        context.Response.ContentType.ShouldBe("application/json");
    }

    [Fact]
    public async Task AuthenticationFailed_WithMalformedToken_ShouldReturnBadRequest()
    {
        // Arrange
        var context = CreateAuthenticationFailedContext(new SecurityTokenMalformedException("Malformed token"));
        
        // Act
        await events.AuthenticationFailed(context);
        
        // Assert
        context.Response.StatusCode.ShouldBe(400);
    }

    private TokenValidatedContext CreateTokenValidatedContext(bool includeUserId = true)
    {
        var httpContext = new DefaultHttpContext();
        var scheme = new Microsoft.AspNetCore.Authentication.AuthenticationScheme("Bearer", "Bearer", typeof(JwtBearerHandler));
        
        var claims = new List<Claim>
        {
            new("email", "<EMAIL>"),
            new("roles", "admin")
        };
        
        if (includeUserId)
        {
            claims.Add(new Claim("sub", "test-user-123"));
        }
        
        var identity = new ClaimsIdentity(claims, "Bearer");
        var principal = new ClaimsPrincipal(identity);
        
        var context = new TokenValidatedContext(httpContext, scheme, new JwtBearerOptions())
        {
            Principal = principal
        };
        
        return context;
    }

    private AuthenticationFailedContext CreateAuthenticationFailedContext(Exception exception)
    {
        var httpContext = new DefaultHttpContext();
        httpContext.Response.Body = new System.IO.MemoryStream();
        
        var scheme = new Microsoft.AspNetCore.Authentication.AuthenticationScheme("Bearer", "Bearer", typeof(JwtBearerHandler));
        
        var context = new AuthenticationFailedContext(httpContext, scheme, new JwtBearerOptions())
        {
            Exception = exception
        };
        
        return context;
    }

    private MessageReceivedContext CreateMessageReceivedContext()
    {
        var httpContext = new DefaultHttpContext();
        var scheme = new Microsoft.AspNetCore.Authentication.AuthenticationScheme("Bearer", "Bearer", typeof(JwtBearerHandler));

        var context = new MessageReceivedContext(httpContext, scheme, new JwtBearerOptions());

        return context;
    }
}
