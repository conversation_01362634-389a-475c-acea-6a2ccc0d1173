# Axon.Core.Authentication
[![Build Status](https://dev.azure.com/Phlexglobal/Axon.Core.AuthenticationAzureProject/_apis/build/status/Axon.Core.Authentication.Build?repoName=Axon.Core.Authentication&branchName=develop)](https://dev.azure.com/Phlexglobal/Axon.Core.AuthenticationAzureProject/_build/latest?definitionId={REPLACE}&repoName=Axon.Core.Authentication&branchName=develop)

## Project Overview

Axon.Core.Authentication is a comprehensive JWT authentication library designed for SmartPhlex applications. It provides easy-to-use middleware components for validating incoming JWTs with extensive configuration options, intelligent error handling, and extensible authentication events. The library supports multiple configuration approaches and includes built-in defaults for common deployment scenarios.

## Features

- **JWT Token Validation**: Comprehensive validation of JWT tokens including signature, expiration, issuer, and audience
- **Configurable Parameters**: Support for configuration through app settings or environment variables with 36+ configuration options
- **Public Key Retrieval**: Automatic fetching and caching of public keys from JWKS endpoints
- **Default Configuration**: Sensible defaults for quick setup with SmartPhlex applications
- **Enhanced JWT Bearer Events**: Custom events with intelligent error handling and status code classification
- **Custom Authentication Callbacks**: Early-stage hooks for custom authentication logic
- **Comprehensive Logging**: Detailed logging for authentication events and errors with configurable levels
- **Thread-Safe Caching**: Efficient caching of public keys with configurable expiration
- **Claims Processing**: Automatic validation of required claims and custom claims addition
- **Database Integration**: Framework for user lookup and claims enhancement from database sources
- **Environment-Specific Defaults**: Pre-configured settings for Development, Testing, and Production environments

## Quick Start

### 1. Installation

Add the package reference to your project:

```xml
<PackageReference Include="Axon.Core.Authentication" Version="1.0.0" />
```

### 2. Configuration Options

The library supports multiple configuration approaches to fit different deployment scenarios:

#### Option 1: Default Configuration (Recommended for SmartPhlex)

```csharp
using Axon.Core.Authentication;

// Uses SmartPhlex defaults with environment-specific settings
services.AddAxonAuthenticationWithDefaults(environment);
```

#### Option 2: Configuration from appsettings.json

```json
{
  "AxonCoreAuthentication": {
    "Issuer": "https://auth.smartphlex.com",
    "Audience": "your-api-name",
    "PublicKeyUrl": "https://auth.smartphlex.com/.well-known/jwks.json",
    "ValidateIssuer": true,
    "ValidateAudience": true,
    "ClockSkew": "00:05:00",
    "RequireHttpsMetadata": true
  }
}
```

```csharp
// In Program.cs or Startup.cs
services.AddAxonAuthentication(configuration);
```

#### Option 3: Environment Variables Configuration

```csharp
// Configure from environment variables only
services.AddAxonAuthenticationFromEnvironment();
```

#### Option 4: Custom Configuration

```csharp
services.AddAxonAuthentication(options =>
{
    options.Issuer = "https://your-auth-server.com";
    options.Audience = "your-api";
    options.PublicKeyUrl = "https://your-auth-server.com/.well-known/jwks.json";
    options.ClockSkew = TimeSpan.FromMinutes(2);
});
```

#### Option 5: Mixed Configuration (appsettings.json + custom)

```csharp
services.AddAxonAuthentication(configuration, options =>
{
    // Override specific settings
    options.ClockSkew = TimeSpan.FromMinutes(10);
    options.IncludeErrorDetails = true;
});
```

### 3. Authentication Setup

```csharp
// In Configure method or Program.cs
app.UseAuthentication();
app.UseAuthorization();
```

### 4. Environment Variables

All configuration options can be set via environment variables with the `AXON_AUTH_` prefix:

#### Core JWT Parameters
- `AXON_AUTH_ISSUER` - Expected token issuer
- `AXON_AUTH_AUDIENCE` - Expected token audience
- `AXON_AUTH_PUBLIC_KEY_URL` - JWKS endpoint URL
- `AXON_AUTH_PUBLIC_KEY` - Direct RSA public key (PEM format)
- `AXON_AUTH_SYMMETRIC_KEY` - HMAC symmetric key (for development)

#### Validation Flags
- `AXON_AUTH_VALIDATE_ISSUER` - Validate issuer claim (true/false)
- `AXON_AUTH_VALIDATE_AUDIENCE` - Validate audience claim (true/false)
- `AXON_AUTH_VALIDATE_LIFETIME` - Validate token expiration (true/false)
- `AXON_AUTH_VALIDATE_SIGNING_KEY` - Validate signature (true/false)
- `AXON_AUTH_REQUIRE_HTTPS` - Require HTTPS for metadata (true/false)
- `AXON_AUTH_SAVE_TOKEN` - Save token in auth properties (true/false)

#### Timing Configuration
- `AXON_AUTH_CLOCK_SKEW_MINUTES` - Clock skew tolerance in minutes
- `AXON_AUTH_CACHE_DURATION_HOURS` - Public key cache duration in hours
- `AXON_AUTH_HTTP_TIMEOUT_SECONDS` - HTTP timeout for key fetching in seconds
- `AXON_AUTH_ACCESS_TOKEN_MINUTES` - Access token expiration in minutes
- `AXON_AUTH_REFRESH_TOKEN_DAYS` - Refresh token expiration in days

#### Claim Configuration
- `AXON_AUTH_USER_ID_CLAIM` - User ID claim type (default: "sub")
- `AXON_AUTH_EMAIL_CLAIM` - Email claim type (default: "email")
- `AXON_AUTH_ROLE_CLAIM` - Role claim type (default: "roles")
- `AXON_AUTH_NAME_CLAIM` - Name claim type (default: "name")
- `AXON_AUTH_GROUPS_CLAIM` - Groups claim type (default: "groups")

#### Advanced Configuration
- `AXON_AUTH_VALID_ISSUERS` - Multiple valid issuers (comma-separated)
- `AXON_AUTH_VALID_AUDIENCES` - Multiple valid audiences (comma-separated)
- `AXON_AUTH_ALGORITHM` - Signing algorithm (default: "RS256")
- `AXON_AUTH_INCLUDE_ERROR_DETAILS` - Include error details in responses (true/false)
- `AXON_AUTH_SCHEME` - Authentication scheme name (default: "Bearer")

## Configuration Options

### Complete Configuration Example

```json
{
  "AxonCoreAuthentication": {
    "Issuer": "https://auth.smartphlex.com",
    "Audience": "smartphlex-api",
    "PublicKeyUrl": "https://auth.smartphlex.com/.well-known/jwks.json",
    "ValidateIssuer": true,
    "ValidateAudience": true,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true,
    "ClockSkew": "00:05:00",
    "PublicKeyCacheDuration": "1.00:00:00",
    "HttpTimeout": "00:00:30",
    "RequireHttpsMetadata": true,
    "UserIdClaimType": "sub",
    "EmailClaimType": "email",
    "RoleClaimType": "roles"
  }
}
```

### Custom Configuration

```csharp
services.AddAxonAuthentication(configuration, options =>
{
    options.Issuer = "https://custom.auth.com";
    options.Audience = "custom-api";
    options.ClockSkew = TimeSpan.FromMinutes(10);
    options.PublicKeyCacheDuration = TimeSpan.FromHours(12);
});
```

### Configuration Properties

| Property | Description | Default Value |
|----------|-------------|---------------|
| `Issuer` | Expected issuer of JWT tokens | `https://auth.smartphlex.com` |
| `Audience` | Expected audience of JWT tokens | `smartphlex-api` |
| `PublicKeyUrl` | URL to fetch public keys for signature verification | `https://auth.smartphlex.com/.well-known/jwks.json` |
| `ValidateIssuer` | Whether to validate the issuer claim | `true` |
| `ValidateAudience` | Whether to validate the audience claim | `true` |
| `ValidateLifetime` | Whether to validate token expiration | `true` |
| `ValidateIssuerSigningKey` | Whether to validate the signing key | `true` |
| `ClockSkew` | Clock skew tolerance for token validation | `5 minutes` |
| `PublicKeyCacheDuration` | How long to cache public keys | `24 hours` |
| `HttpTimeout` | Timeout for HTTP requests to fetch keys | `30 seconds` |
| `RequireHttpsMetadata` | Whether to require HTTPS for metadata URLs | `true` |
| `UserIdClaimType` | Claim type for user identification | `sub` |
| `EmailClaimType` | Claim type for user email | `email` |
| `RoleClaimType` | Claim type for user roles | `roles` |

## Authentication Flow

The library follows the standard JWT Bearer authentication flow with enhanced error handling:

1. **Request Processing**: Incoming requests are processed by the JWT Bearer middleware
2. **Token Extraction**: JWT tokens are extracted from the Authorization header
3. **Token Validation**: Tokens are validated against configured parameters:
   - Signature verification using public keys from JWKS endpoint
   - Issuer validation against configured issuer(s)
   - Audience validation against configured audience(s)
   - Expiration time validation with configurable clock skew
4. **Claims Processing**: After successful validation:
   - Required claims (like user ID) are validated
   - Custom claims can be added through the authentication events
   - Database lookup integration for user information
5. **Authentication Result**: The authentication result is set on the HttpContext

### Error Handling

The library provides intelligent error classification:

- **400 Bad Request**: Malformed JWT tokens
- **401 Unauthorized**: Invalid, expired, or missing tokens
- **500 Internal Server Error**: Configuration or system errors

## Advanced Usage

### Custom Authentication Callback

Implement `IAuthCallback` to add custom logic during JWT authentication processing:

```csharp
public class CustomAuthCallback : IAuthCallback
{
    private readonly IUserService userService;

    public CustomAuthCallback(IUserService userService)
    {
        this.userService = userService;
    }

    public async Task OnAuthReceived(MessageReceivedContext context)
    {
        // Custom logic during message received phase
        // This is called early in the JWT processing pipeline

        // Example: Log authentication attempts
        var token = context.Token;
        if (!string.IsNullOrEmpty(token))
        {
            // Extract user info from token for logging
            var handler = new JwtSecurityTokenHandler();
            if (handler.CanReadToken(token))
            {
                var jsonToken = handler.ReadJwtToken(token);
                var userId = jsonToken.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;

                // Log authentication attempt
                await userService.LogAuthenticationAttemptAsync(userId);
            }
        }
    }
}

// Register the callback
services.AddScoped<IAuthCallback, CustomAuthCallback>();
services.AddAxonAuthentication(configuration);
```

### JWT Bearer Events

The library provides enhanced JWT Bearer events with comprehensive logging and error handling:

#### TokenValidated Event
- **Purpose**: Called after successful JWT token validation
- **Features**:
  - Validates required claims (e.g., user ID)
  - Adds custom claims to the identity
  - Comprehensive logging of authentication success
  - Database user lookup integration

#### AuthenticationFailed Event
- **Purpose**: Called when JWT authentication fails
- **Features**:
  - Intelligent error classification and HTTP status codes
  - Returns 400 for malformed tokens
  - Returns 401 for expired/invalid tokens
  - Detailed error logging with configurable levels

#### Custom Claims Addition
The library automatically processes claims and provides hooks for adding custom claims:

```csharp
// The TokenValidated event automatically:
// 1. Validates required claims (user ID)
// 2. Extracts user information (email, roles)
// 3. Provides extension points for database lookup
// 4. Adds custom claims to the ClaimsIdentity
```

### Protecting Controllers

Use the `[Authorize]` attribute to protect controllers or actions:

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize] // Requires valid JWT token
public class SecureController : ControllerBase
{
    [HttpGet]
    public IActionResult GetSecureData()
    {
        var userId = User.FindFirst("sub")?.Value;
        var email = User.FindFirst("email")?.Value;

        return Ok(new { UserId = userId, Email = email });
    }
}
```

### Manual Token Validation

For scenarios where you need to validate tokens manually:

```csharp
public class TokenValidationService
{
    private readonly IJwtKeyService jwtKeyService;
    private readonly IOptions<JwtAuthenticationOptions> options;

    public TokenValidationService(IJwtKeyService jwtKeyService, IOptions<JwtAuthenticationOptions> options)
    {
        this.jwtKeyService = jwtKeyService;
        this.options = options;
    }

    public async Task<ClaimsPrincipal> ValidateTokenAsync(string token)
    {
        var keys = await jwtKeyService.GetSigningKeysAsync();

        var tokenHandler = new JwtSecurityTokenHandler();
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = options.Value.ValidateIssuer,
            ValidateAudience = options.Value.ValidateAudience,
            ValidateLifetime = options.Value.ValidateLifetime,
            ValidateIssuerSigningKey = options.Value.ValidateIssuerSigningKey,
            ValidIssuer = options.Value.Issuer,
            ValidAudience = options.Value.Audience,
            IssuerSigningKeys = keys,
            ClockSkew = options.Value.ClockSkew
        };

        var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
        return principal;
    }
}
```

## Testing

The library includes comprehensive unit tests and integration tests. To run the tests:

```bash
dotnet test
```

### Test Coverage

- Configuration validation
- JWT token validation scenarios
- Public key retrieval and caching
- Middleware behavior
- Integration tests with test server

## Troubleshooting

### Common Issues

1. **401 Unauthorized responses**
   - Verify the JWT token is properly formatted
   - Check that the issuer and audience match your configuration
   - Ensure the token hasn't expired
   - Verify the public key URL is accessible
   - Check if required claims (like user ID) are present in the token

2. **400 Bad Request responses**
   - Indicates malformed JWT tokens
   - Check token format and structure
   - Verify the token is properly encoded

3. **Public key retrieval failures**
   - Check network connectivity to the public key URL
   - Verify the URL returns valid JWKS format
   - Check if HTTPS is required but HTTP URL is provided

4. **Configuration validation errors**
   - Ensure all required configuration properties are set
   - Verify URLs are properly formatted
   - Check that time spans are positive values
   - Validate that the JwtAuthenticationOptionsValidator is properly registered

5. **Custom callback issues**
   - Ensure IAuthCallback implementation is registered in DI container
   - Check that callback methods handle exceptions properly
   - Verify callback logic doesn't interfere with authentication flow

### Logging

Enable detailed logging to troubleshoot issues:

```json
{
  "Logging": {
    "LogLevel": {
      "Axon.Core.Authentication": "Debug"
    }
  }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.