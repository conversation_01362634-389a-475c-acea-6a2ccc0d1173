# Axon.Core.Authentication
[![Build Status](https://dev.azure.com/Phlexglobal/Axon.Core.AuthenticationAzureProject/_apis/build/status/Axon.Core.Authentication.Build?repoName=Axon.Core.Authentication&branchName=develop)](https://dev.azure.com/Phlexglobal/Axon.Core.AuthenticationAzureProject/_build/latest?definitionId={REPLACE}&repoName=Axon.Core.Authentication&branchName=develop)

## Project Overview

Axon.Core.Authentication is a comprehensive JWT authentication library designed for SmartPhlex applications. It provides easy-to-use middleware components for validating incoming JWTs by checking their signature, expiration, issuer, and audience claims.

## Features

- **JWT Token Validation**: Comprehensive validation of JWT tokens including signature, expiration, issuer, and audience
- **Configurable Parameters**: Support for configuration through app settings or environment variables
- **Public Key Retrieval**: Automatic fetching and caching of public keys from JWKS endpoints
- **Default Configuration**: Sensible defaults for quick setup with SmartPhlex applications
- **Extensible Events**: Custom JWT bearer events for additional authentication logic
- **Comprehensive Logging**: Detailed logging for authentication events and errors
- **Thread-Safe Caching**: Efficient caching of public keys with configurable expiration

## Quick Start

### 1. Installation

Add the package reference to your project:

```xml
<PackageReference Include="Axon.Core.Authentication" Version="1.0.0" />
```

### 2. Basic Configuration

#### Using appsettings.json

```json
{
  "AxonCoreAuthentication": {
    "Issuer": "https://auth.smartphlex.com",
    "Audience": "your-api-name",
    "PublicKeyUrl": "https://auth.smartphlex.com/.well-known/jwks.json"
  }
}
```

#### Using Startup.cs or Program.cs

```csharp
using Axon.Core.Authentication;

// In ConfigureServices method or Program.cs
services.AddAxonAuthentication(configuration);

// In Configure method or Program.cs
app.UseAuthentication();
app.UseAuthorization();
```

### 3. Environment Variables Configuration

Alternatively, configure using environment variables:

```csharp
services.AddAxonAuthenticationFromEnvironment();
```

Supported environment variables:
- `AXON_AUTH_ISSUER`
- `AXON_AUTH_AUDIENCE`
- `AXON_AUTH_PUBLIC_KEY_URL`
- `AXON_AUTH_VALIDATE_ISSUER`
- `AXON_AUTH_VALIDATE_AUDIENCE`
- `AXON_AUTH_VALIDATE_LIFETIME`
- `AXON_AUTH_CLOCK_SKEW_MINUTES`

## Configuration Options

### Complete Configuration Example

```json
{
  "AxonCoreAuthentication": {
    "Issuer": "https://auth.smartphlex.com",
    "Audience": "smartphlex-api",
    "PublicKeyUrl": "https://auth.smartphlex.com/.well-known/jwks.json",
    "ValidateIssuer": true,
    "ValidateAudience": true,
    "ValidateLifetime": true,
    "ValidateIssuerSigningKey": true,
    "ClockSkew": "00:05:00",
    "PublicKeyCacheDuration": "1.00:00:00",
    "HttpTimeout": "00:00:30",
    "RequireHttpsMetadata": true,
    "UserIdClaimType": "sub",
    "EmailClaimType": "email",
    "RoleClaimType": "roles"
  }
}
```

### Custom Configuration

```csharp
services.AddAxonAuthentication(configuration, options =>
{
    options.Issuer = "https://custom.auth.com";
    options.Audience = "custom-api";
    options.ClockSkew = TimeSpan.FromMinutes(10);
    options.PublicKeyCacheDuration = TimeSpan.FromHours(12);
});
```

### Configuration Properties

| Property | Description | Default Value |
|----------|-------------|---------------|
| `Issuer` | Expected issuer of JWT tokens | `https://auth.smartphlex.com` |
| `Audience` | Expected audience of JWT tokens | `smartphlex-api` |
| `PublicKeyUrl` | URL to fetch public keys for signature verification | `https://auth.smartphlex.com/.well-known/jwks.json` |
| `ValidateIssuer` | Whether to validate the issuer claim | `true` |
| `ValidateAudience` | Whether to validate the audience claim | `true` |
| `ValidateLifetime` | Whether to validate token expiration | `true` |
| `ValidateIssuerSigningKey` | Whether to validate the signing key | `true` |
| `ClockSkew` | Clock skew tolerance for token validation | `5 minutes` |
| `PublicKeyCacheDuration` | How long to cache public keys | `24 hours` |
| `HttpTimeout` | Timeout for HTTP requests to fetch keys | `30 seconds` |
| `RequireHttpsMetadata` | Whether to require HTTPS for metadata URLs | `true` |
| `UserIdClaimType` | Claim type for user identification | `sub` |
| `EmailClaimType` | Claim type for user email | `email` |
| `RoleClaimType` | Claim type for user roles | `roles` |

## Advanced Usage

### Custom Authentication Callback

Implement `IAuthCallback` to add custom logic during authentication:

```csharp
public class CustomAuthCallback : IAuthCallback
{
    public async Task OnAuthReceived(TicketReceivedContext context)
    {
        // Add custom claims, validate user in database, etc.
        var userId = context.Principal.FindFirst("sub")?.Value;

        if (!string.IsNullOrEmpty(userId))
        {
            // Look up user in database and add additional claims
            var additionalClaims = await GetUserClaimsFromDatabase(userId);
            var identity = (ClaimsIdentity)context.Principal.Identity;
            identity.AddClaims(additionalClaims);
        }
    }
}

// Register the callback
services.AddScoped<IAuthCallback, CustomAuthCallback>();
services.AddAxonAuthentication(configuration);
```

### Protecting Controllers

Use the `[Authorize]` attribute to protect controllers or actions:

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize] // Requires valid JWT token
public class SecureController : ControllerBase
{
    [HttpGet]
    public IActionResult GetSecureData()
    {
        var userId = User.FindFirst("sub")?.Value;
        var email = User.FindFirst("email")?.Value;

        return Ok(new { UserId = userId, Email = email });
    }
}
```

### Manual Token Validation

For scenarios where you need to validate tokens manually:

```csharp
public class TokenValidationService
{
    private readonly IJwtKeyService jwtKeyService;
    private readonly IOptions<JwtAuthenticationOptions> options;

    public TokenValidationService(IJwtKeyService jwtKeyService, IOptions<JwtAuthenticationOptions> options)
    {
        this.jwtKeyService = jwtKeyService;
        this.options = options;
    }

    public async Task<ClaimsPrincipal> ValidateTokenAsync(string token)
    {
        var keys = await jwtKeyService.GetSigningKeysAsync();

        var tokenHandler = new JwtSecurityTokenHandler();
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = options.Value.ValidateIssuer,
            ValidateAudience = options.Value.ValidateAudience,
            ValidateLifetime = options.Value.ValidateLifetime,
            ValidateIssuerSigningKey = options.Value.ValidateIssuerSigningKey,
            ValidIssuer = options.Value.Issuer,
            ValidAudience = options.Value.Audience,
            IssuerSigningKeys = keys,
            ClockSkew = options.Value.ClockSkew
        };

        var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
        return principal;
    }
}
```

## Testing

The library includes comprehensive unit tests and integration tests. To run the tests:

```bash
dotnet test
```

### Test Coverage

- Configuration validation
- JWT token validation scenarios
- Public key retrieval and caching
- Middleware behavior
- Integration tests with test server

## Troubleshooting

### Common Issues

1. **401 Unauthorized responses**
   - Verify the JWT token is properly formatted
   - Check that the issuer and audience match your configuration
   - Ensure the token hasn't expired
   - Verify the public key URL is accessible

2. **Public key retrieval failures**
   - Check network connectivity to the public key URL
   - Verify the URL returns valid JWKS format
   - Check if HTTPS is required but HTTP URL is provided

3. **Configuration validation errors**
   - Ensure all required configuration properties are set
   - Verify URLs are properly formatted
   - Check that time spans are positive values

### Logging

Enable detailed logging to troubleshoot issues:

```json
{
  "Logging": {
    "LogLevel": {
      "Axon.Core.Authentication": "Debug"
    }
  }
}
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.