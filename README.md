# Axon.Core.Authentication

A comprehensive JWT authentication library for .NET applications with intelligent error handling, extensive configuration options, and custom authentication callbacks.

## Features

- **JWT Token Validation** - Signature, expiration, issuer, and audience validation
- **Flexible Configuration** - App settings, environment variables, or code-based configuration
- **Public Key Management** - Automatic JWKS endpoint fetching with caching
- **Smart Error Handling** - Returns 400 for malformed tokens, 401 for invalid/expired tokens
- **Custom Callbacks** - Extensible authentication hooks for custom logic
- **Claims Processing** - Automatic validation and custom claims addition
- **Comprehensive Logging** - Detailed authentication event logging

## Quick Start

### 1. Installation

```xml
<PackageReference Include="Axon.Core.Authentication" Version="1.0.0" />
```

### 2. Basic Setup

```csharp
// Program.cs
using Axon.Core.Authentication;

// Option 1: Custom configuration
builder.Services.AddAxonAuthentication(options =>
{
    options.Issuer = "https://your-auth-server.com";
    options.Audience = "your-api";
    options.PublicKeyUrl = "https://your-auth-server.com/.well-known/jwks.json";
});

// Option 2: From appsettings.json
builder.Services.AddAxonAuthentication(builder.Configuration);

var app = builder.Build();

app.UseAuthentication();
app.UseAuthorization();
```

### 3. Configuration (appsettings.json)

```json
{
  "AxonCoreAuthentication": {
    "Issuer": "https://auth.smartphlex.com",
    "Audience": "your-api-name",
    "PublicKeyUrl": "https://auth.smartphlex.com/.well-known/jwks.json",
    "ClockSkew": "00:05:00"
  }
}
```

## Usage

### Protecting Controllers

```csharp
[ApiController]
[Route("api/[controller]")]
[Authorize] // Requires valid JWT token
public class SecureController : ControllerBase
{
    [HttpGet]
    public IActionResult GetUserInfo()
    {
        var userId = User.FindFirst("sub")?.Value;
        var email = User.FindFirst("email")?.Value;
        return Ok(new { UserId = userId, Email = email });
    }
}
```

### Custom Authentication Callback

Implement `IAuthCallback` for custom authentication logic:

```csharp
public class CustomAuthCallback : IAuthCallback
{
    private readonly IUserService userService;

    public CustomAuthCallback(IUserService userService)
    {
        this.userService = userService;
    }

    public async Task OnAuthReceived(MessageReceivedContext context)
    {
        // Custom logic during JWT processing
        var token = context.Token;
        if (!string.IsNullOrEmpty(token))
        {
            var handler = new JwtSecurityTokenHandler();
            if (handler.CanReadToken(token))
            {
                var jsonToken = handler.ReadJwtToken(token);
                var userId = jsonToken.Claims.FirstOrDefault(c => c.Type == "sub")?.Value;
                await userService.LogAuthenticationAttemptAsync(userId);
            }
        }
    }
}

// Register the callback
services.AddScoped<IAuthCallback, CustomAuthCallback>();
services.AddAxonAuthentication(configuration);
```

## Error Handling

The library provides intelligent error classification:

- **400 Bad Request**: Malformed JWT tokens
- **401 Unauthorized**: Invalid, expired, or missing tokens
- **500 Internal Server Error**: Configuration or system errors

## Testing

Run tests with:

```bash
dotnet test
```

## Troubleshooting

### Common Issues

- **401 Unauthorized**: Check token format, issuer/audience configuration, expiration, and required claims
- **400 Bad Request**: Indicates malformed JWT tokens
- **Public key failures**: Verify network connectivity and JWKS endpoint accessibility
- **Configuration errors**: Ensure all required properties are set and properly formatted

### Logging

Enable debug logging:

```json
{
  "Logging": {
    "LogLevel": {
      "Axon.Core.Authentication": "Debug"
    }
  }
}
```