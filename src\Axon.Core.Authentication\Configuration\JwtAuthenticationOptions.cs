using System;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Authentication.Configuration;

public class JwtAuthenticationOptions
{
    public const string SECTION_NAME = "AxonCoreAuthentication";

    public string Issuer { get; set; } = string.Empty;

    public string Audience { get; set; } = string.Empty;

    [Required]
    public string PublicKey { get; set; } = string.Empty;

    public bool ValidateIssuer { get; set; } = true;

    public bool ValidateAudience { get; set; } = true;

    public bool ValidateLifetime { get; set; } = true;

    public bool ValidateIssuerSigningKey { get; set; } = true;

    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromMinutes(5);

    public TimeSpan PublicKeyCacheDuration { get; set; } = TimeSpan.FromHours(24);

    public TimeSpan AccessTokenExpirationMinutes { get; set; } = TimeSpan.FromMinutes(15);

    public TimeSpan RefreshTokenExpirationDays { get; set; } = TimeSpan.FromDays(7);

    public string UserIdClaimType { get; set; } = "sub";

    public string EmailClaimType { get; set; } = "email";

    public string RoleClaimType { get; set; } = "roles";
}
