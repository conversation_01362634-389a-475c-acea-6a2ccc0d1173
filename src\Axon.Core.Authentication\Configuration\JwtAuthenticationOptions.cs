using System;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Authentication.Configuration;

/// <summary>
/// Configuration options for JWT authentication in Axon applications.
/// </summary>
public class JwtAuthenticationOptions
{
    /// <summary>
    /// Configuration section name for JWT authentication settings.
    /// </summary>
    public const string SECTION_NAME = "AxonCoreAuthentication";

    /// <summary>
    /// The expected issuer of the JWT token.
    /// Default: "https://auth.smartphlex.com"
    /// </summary>
    public string Issuer { get; set; } = "https://auth.smartphlex.com";

    /// <summary>
    /// The expected audience of the JWT token.
    /// Default: "smartphlex-api"
    /// </summary>
    public string Audience { get; set; } = "smartphlex-api";

    /// <summary>
    /// URL to fetch the public key for JWT signature verification.
    /// Default: "https://auth.smartphlex.com/.well-known/jwks.json"
    /// </summary>
    [Required]
    [Url]
    public string PublicKeyUrl { get; set; } = "https://auth.smartphlex.com/.well-known/jwks.json";

    /// <summary>
    /// Whether to validate the issuer claim in the JWT token.
    /// Default: true
    /// </summary>
    public bool ValidateIssuer { get; set; } = true;

    /// <summary>
    /// Whether to validate the audience claim in the JWT token.
    /// Default: true
    /// </summary>
    public bool ValidateAudience { get; set; } = true;

    /// <summary>
    /// Whether to validate the token lifetime (expiration).
    /// Default: true
    /// </summary>
    public bool ValidateLifetime { get; set; } = true;

    /// <summary>
    /// Whether to validate the issuer signing key.
    /// Default: true
    /// </summary>
    public bool ValidateIssuerSigningKey { get; set; } = true;

    /// <summary>
    /// Clock skew tolerance for token validation.
    /// Default: 5 minutes
    /// </summary>
    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Cache duration for public keys retrieved from the PublicKeyUrl.
    /// Default: 24 hours
    /// </summary>
    public TimeSpan PublicKeyCacheDuration { get; set; } = TimeSpan.FromHours(24);

    /// <summary>
    /// Timeout for HTTP requests to fetch public keys.
    /// Default: 30 seconds
    /// </summary>
    public TimeSpan HttpTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Whether to require HTTPS for the public key URL.
    /// Default: true
    /// </summary>
    public bool RequireHttpsMetadata { get; set; } = true;

    /// <summary>
    /// Custom claim type for user identification.
    /// Default: "sub" (subject)
    /// </summary>
    public string UserIdClaimType { get; set; } = "sub";

    /// <summary>
    /// Custom claim type for user email.
    /// Default: "email"
    /// </summary>
    public string EmailClaimType { get; set; } = "email";

    /// <summary>
    /// Custom claim type for user roles.
    /// Default: "roles"
    /// </summary>
    public string RoleClaimType { get; set; } = "roles";
}
