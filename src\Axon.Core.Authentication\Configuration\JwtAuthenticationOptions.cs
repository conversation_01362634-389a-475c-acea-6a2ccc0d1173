using System;
using System.ComponentModel.DataAnnotations;

namespace Axon.Core.Authentication.Configuration;

/// <summary>
/// Configuration options for JWT authentication with comprehensive validation parameters.
/// Supports configuration through appsettings.json and environment variables.
/// </summary>
public class JwtAuthenticationOptions
{
    /// <summary>
    /// Configuration section name for appsettings.json
    /// </summary>
    public const string SECTION_NAME = "AxonCoreAuthentication";

    #region Core JWT Validation Parameters

    /// <summary>
    /// Expected issuer of JWT tokens. Can be set via AXON_AUTH_ISSUER environment variable.
    /// </summary>
    public string Issuer { get; set; } = "https://auth.smartphlex.com";

    /// <summary>
    /// Expected audience of JWT tokens. Can be set via AXON_AUTH_AUDIENCE environment variable.
    /// </summary>
    public string Audience { get; set; } = "smartphlex-api";

    /// <summary>
    /// URL to fetch public keys (JWKS) for signature verification.
    /// Can be set via AXON_AUTH_PUBLIC_KEY_URL environment variable.
    /// </summary>
    public string PublicKeyUrl { get; set; } = "https://auth.smartphlex.com/.well-known/jwks.json";

    /// <summary>
    /// Direct public key for signature verification (alternative to PublicKeyUrl).
    /// Can be set via AXON_AUTH_PUBLIC_KEY environment variable.
    /// </summary>
    public string PublicKey { get; set; } = string.Empty;

    /// <summary>
    /// Symmetric key for HMAC signature verification (for development/testing).
    /// Can be set via AXON_AUTH_SYMMETRIC_KEY environment variable.
    /// </summary>
    public string SymmetricKey { get; set; } = string.Empty;

    #endregion

    #region Validation Flags

    /// <summary>
    /// Whether to validate the issuer claim. Can be set via AXON_AUTH_VALIDATE_ISSUER environment variable.
    /// </summary>
    public bool ValidateIssuer { get; set; } = true;

    /// <summary>
    /// Whether to validate the audience claim. Can be set via AXON_AUTH_VALIDATE_AUDIENCE environment variable.
    /// </summary>
    public bool ValidateAudience { get; set; } = true;

    /// <summary>
    /// Whether to validate token expiration. Can be set via AXON_AUTH_VALIDATE_LIFETIME environment variable.
    /// </summary>
    public bool ValidateLifetime { get; set; } = true;

    /// <summary>
    /// Whether to validate the signing key. Can be set via AXON_AUTH_VALIDATE_SIGNING_KEY environment variable.
    /// </summary>
    public bool ValidateIssuerSigningKey { get; set; } = true;

    /// <summary>
    /// Whether to require HTTPS for metadata endpoints. Can be set via AXON_AUTH_REQUIRE_HTTPS environment variable.
    /// </summary>
    public bool RequireHttpsMetadata { get; set; } = true;

    /// <summary>
    /// Whether to save the token in the authentication properties. Can be set via AXON_AUTH_SAVE_TOKEN environment variable.
    /// </summary>
    public bool SaveToken { get; set; } = true;

    #endregion

    #region Timing and Caching Configuration

    /// <summary>
    /// Clock skew tolerance for token validation. Can be set via AXON_AUTH_CLOCK_SKEW_MINUTES environment variable.
    /// </summary>
    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Duration to cache public keys. Can be set via AXON_AUTH_CACHE_DURATION_HOURS environment variable.
    /// </summary>
    public TimeSpan PublicKeyCacheDuration { get; set; } = TimeSpan.FromHours(24);

    /// <summary>
    /// HTTP timeout for fetching public keys. Can be set via AXON_AUTH_HTTP_TIMEOUT_SECONDS environment variable.
    /// </summary>
    public TimeSpan HttpTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Access token expiration time (for token generation). Can be set via AXON_AUTH_ACCESS_TOKEN_MINUTES environment variable.
    /// </summary>
    public TimeSpan AccessTokenExpirationMinutes { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Refresh token expiration time (for token generation). Can be set via AXON_AUTH_REFRESH_TOKEN_DAYS environment variable.
    /// </summary>
    public TimeSpan RefreshTokenExpirationDays { get; set; } = TimeSpan.FromDays(7);

    #endregion

    #region Claim Configuration

    /// <summary>
    /// Claim type for user identifier. Can be set via AXON_AUTH_USER_ID_CLAIM environment variable.
    /// </summary>
    public string UserIdClaimType { get; set; } = "sub";

    /// <summary>
    /// Claim type for user email. Can be set via AXON_AUTH_EMAIL_CLAIM environment variable.
    /// </summary>
    public string EmailClaimType { get; set; } = "email";

    /// <summary>
    /// Claim type for user roles. Can be set via AXON_AUTH_ROLE_CLAIM environment variable.
    /// </summary>
    public string RoleClaimType { get; set; } = "roles";

    /// <summary>
    /// Claim type for user name. Can be set via AXON_AUTH_NAME_CLAIM environment variable.
    /// </summary>
    public string NameClaimType { get; set; } = "name";

    /// <summary>
    /// Claim type for user groups. Can be set via AXON_AUTH_GROUPS_CLAIM environment variable.
    /// </summary>
    public string GroupsClaimType { get; set; } = "groups";

    #endregion

    #region Advanced Configuration

    /// <summary>
    /// Multiple valid issuers (comma-separated). Can be set via AXON_AUTH_VALID_ISSUERS environment variable.
    /// </summary>
    public string ValidIssuers { get; set; } = string.Empty;

    /// <summary>
    /// Multiple valid audiences (comma-separated). Can be set via AXON_AUTH_VALID_AUDIENCES environment variable.
    /// </summary>
    public string ValidAudiences { get; set; } = string.Empty;

    /// <summary>
    /// Algorithm used for token signing. Can be set via AXON_AUTH_ALGORITHM environment variable.
    /// </summary>
    public string Algorithm { get; set; } = "RS256";

    /// <summary>
    /// Whether to include error details in authentication failures. Can be set via AXON_AUTH_INCLUDE_ERROR_DETAILS environment variable.
    /// </summary>
    public bool IncludeErrorDetails { get; set; } = false;

    /// <summary>
    /// Custom authentication scheme name. Can be set via AXON_AUTH_SCHEME environment variable.
    /// </summary>
    public string AuthenticationScheme { get; set; } = "Bearer";

    #endregion

    #region Environment Variable Keys (for reference)

    /// <summary>
    /// Environment variable keys for configuration
    /// </summary>
    public static class EnvironmentVariables
    {
        public const string Issuer = "AXON_AUTH_ISSUER";
        public const string Audience = "AXON_AUTH_AUDIENCE";
        public const string PublicKeyUrl = "AXON_AUTH_PUBLIC_KEY_URL";
        public const string PublicKey = "AXON_AUTH_PUBLIC_KEY";
        public const string SymmetricKey = "AXON_AUTH_SYMMETRIC_KEY";
        public const string ValidateIssuer = "AXON_AUTH_VALIDATE_ISSUER";
        public const string ValidateAudience = "AXON_AUTH_VALIDATE_AUDIENCE";
        public const string ValidateLifetime = "AXON_AUTH_VALIDATE_LIFETIME";
        public const string ValidateSigningKey = "AXON_AUTH_VALIDATE_SIGNING_KEY";
        public const string RequireHttpsMetadata = "AXON_AUTH_REQUIRE_HTTPS";
        public const string SaveToken = "AXON_AUTH_SAVE_TOKEN";
        public const string ClockSkewMinutes = "AXON_AUTH_CLOCK_SKEW_MINUTES";
        public const string CacheDurationHours = "AXON_AUTH_CACHE_DURATION_HOURS";
        public const string HttpTimeoutSeconds = "AXON_AUTH_HTTP_TIMEOUT_SECONDS";
        public const string AccessTokenMinutes = "AXON_AUTH_ACCESS_TOKEN_MINUTES";
        public const string RefreshTokenDays = "AXON_AUTH_REFRESH_TOKEN_DAYS";
        public const string UserIdClaim = "AXON_AUTH_USER_ID_CLAIM";
        public const string EmailClaim = "AXON_AUTH_EMAIL_CLAIM";
        public const string RoleClaim = "AXON_AUTH_ROLE_CLAIM";
        public const string NameClaim = "AXON_AUTH_NAME_CLAIM";
        public const string GroupsClaim = "AXON_AUTH_GROUPS_CLAIM";
        public const string ValidIssuers = "AXON_AUTH_VALID_ISSUERS";
        public const string ValidAudiences = "AXON_AUTH_VALID_AUDIENCES";
        public const string Algorithm = "AXON_AUTH_ALGORITHM";
        public const string IncludeErrorDetails = "AXON_AUTH_INCLUDE_ERROR_DETAILS";
        public const string AuthenticationScheme = "AXON_AUTH_SCHEME";
    }

    #endregion
}
