using System;

namespace Axon.Core.Authentication.Configuration;

public class JwtAuthenticationOptions
{
    public const string AxonCoreAuthenticationConfigKey = "AxonCoreAuthentication";

    public string Issuer { get; set; } = string.Empty;
    public string Audience { get; set; } = string.Empty;
    public string PublicKeyUrl { get; set; } = string.Empty;
    public string PublicKey { get; set; } = string.Empty;
    public string SymmetricKey { get; set; } = string.Empty;

    public bool ValidateIssuer { get; set; } = true;
    public bool ValidateAudience { get; set; } = true;
    public bool ValidateLifetime { get; set; } = true;
    public bool ValidateIssuerSigningKey { get; set; } = true;
    public bool RequireHttpsMetadata { get; set; } = true;
    public bool SaveToken { get; set; } = true;

    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromMinutes(5);
    public TimeSpan PublicKeyCacheDuration { get; set; } = TimeSpan.FromHours(24);
    public TimeSpan HttpTimeout { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan AccessTokenExpirationMinutes { get; set; } = TimeSpan.FromMinutes(15);
    public TimeSpan RefreshTokenExpirationDays { get; set; } = TimeSpan.FromDays(7);

    public string UserIdClaimType { get; set; } = "sub";
    public string EmailClaimType { get; set; } = "email";
    public string NameClaimType { get; set; } = "name";

    public string ValidIssuers { get; set; } = string.Empty;
    public string ValidAudiences { get; set; } = string.Empty;
    public string Algorithm { get; set; } = "RS256";
    public string AuthenticationScheme { get; set; } = "Bearer";
}
