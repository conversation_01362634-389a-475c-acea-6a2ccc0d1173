﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32228.430
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{BF3DAFC7-**************-662780A57999}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "config", "config", "{CD00D495-865C-471F-AF4F-93260F96214C}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		azure-pipelines.yml = azure-pipelines.yml
		Axon.Core.Authentication.sln.DotSettings = Axon.Core.Authentication.sln.DotSettings
		README.md = README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.Core.Authentication", "src\Axon.Core.Authentication\Axon.Core.Authentication.csproj", "{36F50C07-9DF7-47D4-9258-5F217309F5F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Axon.Core.Authentication.Tests", "test\Axon.Core.Authentication.Tests\Axon.Core.Authentication.Tests.csproj", "{C904952F-451C-4DA8-8536-D1FB1959D448}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{36F50C07-9DF7-47D4-9258-5F217309F5F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{36F50C07-9DF7-47D4-9258-5F217309F5F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{36F50C07-9DF7-47D4-9258-5F217309F5F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{36F50C07-9DF7-47D4-9258-5F217309F5F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{C904952F-451C-4DA8-8536-D1FB1959D448}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C904952F-451C-4DA8-8536-D1FB1959D448}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C904952F-451C-4DA8-8536-D1FB1959D448}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C904952F-451C-4DA8-8536-D1FB1959D448}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{36F50C07-9DF7-47D4-9258-5F217309F5F5} = {CE3A4BC1-F263-4530-9ABC-8B972E3BF3FB}
		{C904952F-451C-4DA8-8536-D1FB1959D448} = {BF3DAFC7-**************-662780A57999}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {017230C6-8D38-4A98-A6B9-AD7C073DF3EF}
	EndGlobalSection
EndGlobal
