using Axon.Core.Authentication;
using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Shouldly;
using Xunit;

namespace Axon.Core.Authentication.Tests;

public class ServiceCollectionExtensionsTests
{
    [Fact]
    public void AddAxonAuthentication_WithConfiguration_ShouldRegisterServices()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["AxonCoreAuthentication:Issuer"] = "https://test.auth.com",
                ["AxonCoreAuthentication:Audience"] = "test-api",
                ["AxonCoreAuthentication:PublicKeyUrl"] = "https://test.auth.com/.well-known/jwks.json"
            })
            .Build();

        // Act
        services.AddAxonAuthentication(configuration);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // Verify JWT options are configured
        var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>();
        jwtOptions.Value.Issuer.ShouldBe("https://test.auth.com");
        jwtOptions.Value.Audience.ShouldBe("test-api");
        jwtOptions.Value.PublicKeyUrl.ShouldBe("https://test.auth.com/.well-known/jwks.json");

        // Verify services are registered
        serviceProvider.GetRequiredService<IJwtKeyService>().ShouldNotBeNull();
        serviceProvider.GetRequiredService<IAuthenticationSchemeProvider>().ShouldNotBeNull();
    }

    [Fact]
    public void AddAxonAuthentication_WithCustomConfiguration_ShouldApplyCustomOptions()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder().Build();

        // Act
        services.AddAxonAuthentication(configuration, options =>
        {
            options.Issuer = "https://custom.auth.com";
            options.Audience = "custom-api";
            options.ClockSkew = TimeSpan.FromMinutes(10);
        });

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>();
        
        jwtOptions.Value.Issuer.ShouldBe("https://custom.auth.com");
        jwtOptions.Value.Audience.ShouldBe("custom-api");
        jwtOptions.Value.ClockSkew.ShouldBe(TimeSpan.FromMinutes(10));
    }

    [Fact]
    public void AddAxonAuthenticationFromEnvironment_ShouldConfigureFromEnvironmentVariables()
    {
        // Arrange
        var services = new ServiceCollection();
        
        // Set environment variables
        Environment.SetEnvironmentVariable("AXON_AUTH_ISSUER", "https://env.auth.com");
        Environment.SetEnvironmentVariable("AXON_AUTH_AUDIENCE", "env-api");
        Environment.SetEnvironmentVariable("AXON_AUTH_PUBLIC_KEY_URL", "https://env.auth.com/keys");
        Environment.SetEnvironmentVariable("AXON_AUTH_VALIDATE_ISSUER", "false");
        Environment.SetEnvironmentVariable("AXON_AUTH_CLOCK_SKEW_MINUTES", "15");

        try
        {
            // Act
            services.AddAxonAuthenticationFromEnvironment();

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>();
            
            jwtOptions.Value.Issuer.ShouldBe("https://env.auth.com");
            jwtOptions.Value.Audience.ShouldBe("env-api");
            jwtOptions.Value.PublicKeyUrl.ShouldBe("https://env.auth.com/keys");
            jwtOptions.Value.ValidateIssuer.ShouldBeFalse();
            jwtOptions.Value.ClockSkew.ShouldBe(TimeSpan.FromMinutes(15));
        }
        finally
        {
            // Cleanup environment variables
            Environment.SetEnvironmentVariable("AXON_AUTH_ISSUER", null);
            Environment.SetEnvironmentVariable("AXON_AUTH_AUDIENCE", null);
            Environment.SetEnvironmentVariable("AXON_AUTH_PUBLIC_KEY_URL", null);
            Environment.SetEnvironmentVariable("AXON_AUTH_VALIDATE_ISSUER", null);
            Environment.SetEnvironmentVariable("AXON_AUTH_CLOCK_SKEW_MINUTES", null);
        }
    }

    [Fact]
    public void AddAxonAuthenticationFromEnvironment_WithCustomConfiguration_ShouldApplyBothEnvironmentAndCustomOptions()
    {
        // Arrange
        var services = new ServiceCollection();
        
        Environment.SetEnvironmentVariable("AXON_AUTH_ISSUER", "https://env.auth.com");
        Environment.SetEnvironmentVariable("AXON_AUTH_AUDIENCE", "env-api");

        try
        {
            // Act
            services.AddAxonAuthenticationFromEnvironment(options =>
            {
                options.ClockSkew = TimeSpan.FromMinutes(20);
                options.ValidateLifetime = false;
            });

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>();
            
            // Environment variables should be applied
            jwtOptions.Value.Issuer.ShouldBe("https://env.auth.com");
            jwtOptions.Value.Audience.ShouldBe("env-api");
            
            // Custom configuration should also be applied
            jwtOptions.Value.ClockSkew.ShouldBe(TimeSpan.FromMinutes(20));
            jwtOptions.Value.ValidateLifetime.ShouldBeFalse();
        }
        finally
        {
            Environment.SetEnvironmentVariable("AXON_AUTH_ISSUER", null);
            Environment.SetEnvironmentVariable("AXON_AUTH_AUDIENCE", null);
        }
    }

    [Fact]
    public void AddAxonAuthentication_ShouldRegisterCorrectAuthenticationScheme()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder().Build();

        // Act
        services.AddAxonAuthentication(configuration);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var schemeProvider = serviceProvider.GetRequiredService<IAuthenticationSchemeProvider>();
        
        var scheme = schemeProvider.GetSchemeAsync(JwtBearerAuthenticationDefaults.AuthenticationScheme).Result;
        scheme.ShouldNotBeNull();
        scheme.Name.ShouldBe(JwtBearerAuthenticationDefaults.AuthenticationScheme);
    }

    [Fact]
    public void AddAxonAuthentication_ShouldRegisterHttpClientForJwtKeyService()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder().Build();

        // Act
        services.AddAxonAuthentication(configuration);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        
        // Should be able to resolve IJwtKeyService which depends on HttpClient
        var jwtKeyService = serviceProvider.GetRequiredService<IJwtKeyService>();
        jwtKeyService.ShouldNotBeNull();
        jwtKeyService.ShouldBeOfType<JwtKeyService>();
    }

    [Fact]
    public void AddAxonAuthentication_WithDefaultConfiguration_ShouldUseDefaultValues()
    {
        // Arrange
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder().Build(); // Empty configuration

        // Act
        services.AddAxonAuthentication(configuration);

        // Assert
        var serviceProvider = services.BuildServiceProvider();
        var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>();
        
        // Should use default values
        jwtOptions.Value.Issuer.ShouldBe("https://auth.smartphlex.com");
        jwtOptions.Value.Audience.ShouldBe("smartphlex-api");
        jwtOptions.Value.PublicKeyUrl.ShouldBe("https://auth.smartphlex.com/.well-known/jwks.json");
        jwtOptions.Value.ValidateIssuer.ShouldBeTrue();
        jwtOptions.Value.ValidateAudience.ShouldBeTrue();
        jwtOptions.Value.ValidateLifetime.ShouldBeTrue();
        jwtOptions.Value.ValidateIssuerSigningKey.ShouldBeTrue();
    }
}
