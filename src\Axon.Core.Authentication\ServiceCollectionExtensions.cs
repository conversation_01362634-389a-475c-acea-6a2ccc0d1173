﻿using System;
using Axon.Core.Authentication.Events;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.Text;

namespace Axon.Core.Authentication;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAxonAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        using var loggerFactory = LoggerFactory.Create(loggingBuilder => loggingBuilder
            .SetMinimumLevel(LogLevel.Trace).AddConsole());
        var jwtEventsLogger = loggerFactory.CreateLogger<AxonJwtBearerEvents>();

        // fetch public key from cache/Axon endpoint?
        var rsaKey = string.Empty;

        services.AddAuthentication(JwtBearerAuthenticationDefaults.AuthenticationScheme)
            .AddJwtBearer(JwtBearerAuthenticationDefaults.AuthenticationScheme, options =>
            {
                options.Audience = configuration["AxonCoreAuthentication:Audience"];

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = true,
                    ValidateLifetime = true,
                    ValidateAudience = true,
                    ValidAudience = configuration["AxonCoreAuthentication:Scope"],
                    ValidIssuer = configuration["AxonCoreAuthentication:RootUrl"],
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(rsaKey)),
                    ClockSkew = TimeSpan.Zero
                };

                options.Events = new AxonJwtBearerEvents(jwtEventsLogger);
            });

        return services;
    }
}