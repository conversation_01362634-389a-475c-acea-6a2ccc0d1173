﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Events;
using Axon.Core.Authentication.Interfaces;
using Axon.Core.Authentication.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace Axon.Core.Authentication;

/// <summary>
/// Extension methods for configuring Axon JWT authentication services.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Axon JWT authentication services to the service collection with default configuration.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The configuration instance.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddAxonAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        return services.AddAxonAuthentication(configuration, null);
    }

    /// <summary>
    /// Adds Axon JWT authentication services to the service collection with custom configuration.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The configuration instance.</param>
    /// <param name="configureOptions">Optional action to configure JWT authentication options.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddAxonAuthentication(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<JwtAuthenticationOptions>? configureOptions)
    {
        // Configure JWT authentication options
        var optionsSection = configuration.GetSection(JwtAuthenticationOptions.SECTION_NAME);
        services.Configure<JwtAuthenticationOptions>(optionsSection);

        // Apply custom configuration if provided
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }

        // Validate configuration
        services.AddSingleton<IValidateOptions<JwtAuthenticationOptions>, JwtAuthenticationOptionsValidator>();

        // Register HTTP client for JWT key service
        services.AddHttpClient<IJwtKeyService, JwtKeyService>();

        // Register JWT key service
        services.AddSingleton<IJwtKeyService, JwtKeyService>();

        // Configure authentication
        services.AddAuthentication(JwtBearerAuthenticationDefaults.AuthenticationScheme)
            .AddJwtBearer(JwtBearerAuthenticationDefaults.AuthenticationScheme, (options, serviceProvider) =>
            {
                var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>().Value;
                var logger = serviceProvider.GetRequiredService<ILogger<AxonJwtBearerEvents>>();
                var authCallback = serviceProvider.GetService<IAuthCallback>();
                var jwtKeyService = serviceProvider.GetRequiredService<IJwtKeyService>();

                ConfigureJwtBearerOptions(options, jwtOptions, jwtKeyService);
                options.Events = new AxonJwtBearerEvents(logger, serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>(), authCallback);
            });

        return services;
    }

    /// <summary>
    /// Adds Axon JWT authentication services with environment variable configuration.
    /// This method reads configuration from environment variables with the prefix "AXON_AUTH_".
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configureOptions">Optional action to configure JWT authentication options.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddAxonAuthenticationFromEnvironment(
        this IServiceCollection services,
        Action<JwtAuthenticationOptions>? configureOptions = null)
    {
        // Configure options from environment variables
        services.Configure<JwtAuthenticationOptions>(options =>
        {
            var issuer = Environment.GetEnvironmentVariable("AXON_AUTH_ISSUER");
            if (!string.IsNullOrEmpty(issuer))
                options.Issuer = issuer;

            var audience = Environment.GetEnvironmentVariable("AXON_AUTH_AUDIENCE");
            if (!string.IsNullOrEmpty(audience))
                options.Audience = audience;

            var publicKeyUrl = Environment.GetEnvironmentVariable("AXON_AUTH_PUBLIC_KEY_URL");
            if (!string.IsNullOrEmpty(publicKeyUrl))
                options.PublicKeyUrl = publicKeyUrl;

            var validateIssuer = Environment.GetEnvironmentVariable("AXON_AUTH_VALIDATE_ISSUER");
            if (bool.TryParse(validateIssuer, out var validateIssuerValue))
                options.ValidateIssuer = validateIssuerValue;

            var validateAudience = Environment.GetEnvironmentVariable("AXON_AUTH_VALIDATE_AUDIENCE");
            if (bool.TryParse(validateAudience, out var validateAudienceValue))
                options.ValidateAudience = validateAudienceValue;

            var validateLifetime = Environment.GetEnvironmentVariable("AXON_AUTH_VALIDATE_LIFETIME");
            if (bool.TryParse(validateLifetime, out var validateLifetimeValue))
                options.ValidateLifetime = validateLifetimeValue;

            var clockSkew = Environment.GetEnvironmentVariable("AXON_AUTH_CLOCK_SKEW_MINUTES");
            if (int.TryParse(clockSkew, out var clockSkewMinutes))
                options.ClockSkew = TimeSpan.FromMinutes(clockSkewMinutes);
        });

        // Apply custom configuration if provided
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }

        // Register remaining services
        services.AddSingleton<IValidateOptions<JwtAuthenticationOptions>, JwtAuthenticationOptionsValidator>();
        services.AddHttpClient<IJwtKeyService, JwtKeyService>();
        services.AddSingleton<IJwtKeyService, JwtKeyService>();

        // Configure authentication
        services.AddAuthentication(JwtBearerAuthenticationDefaults.AuthenticationScheme)
            .AddJwtBearer(JwtBearerAuthenticationDefaults.AuthenticationScheme, (options, serviceProvider) =>
            {
                var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>().Value;
                var logger = serviceProvider.GetRequiredService<ILogger<AxonJwtBearerEvents>>();
                var authCallback = serviceProvider.GetService<IAuthCallback>();
                var jwtKeyService = serviceProvider.GetRequiredService<IJwtKeyService>();

                ConfigureJwtBearerOptions(options, jwtOptions, jwtKeyService);
                options.Events = new AxonJwtBearerEvents(logger, serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>(), authCallback);
            });

        return services;
    }

    private static void ConfigureJwtBearerOptions(
        JwtBearerOptions options,
        JwtAuthenticationOptions jwtOptions,
        IJwtKeyService jwtKeyService)
    {
        options.Audience = jwtOptions.Audience;
        options.ClaimsIssuer = jwtOptions.Issuer;
        options.RequireHttpsMetadata = jwtOptions.RequireHttpsMetadata;

        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = jwtOptions.ValidateIssuer,
            ValidateLifetime = jwtOptions.ValidateLifetime,
            ValidateAudience = jwtOptions.ValidateAudience,
            ValidateIssuerSigningKey = jwtOptions.ValidateIssuerSigningKey,
            ValidAudience = jwtOptions.Audience,
            ValidIssuer = jwtOptions.Issuer,
            ClockSkew = jwtOptions.ClockSkew,
            NameClaimType = jwtOptions.UserIdClaimType,
            RoleClaimType = jwtOptions.RoleClaimType,

            // Use async key resolver for dynamic key retrieval
            IssuerSigningKeyResolver = (token, securityToken, kid, parameters) =>
            {
                // This will be called synchronously, so we need to handle async calls carefully
                var keys = Task.Run(async () => await jwtKeyService.GetSigningKeysAsync()).GetAwaiter().GetResult();
                return keys;
            }
        };
    }
}