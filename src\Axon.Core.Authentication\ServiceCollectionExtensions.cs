﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;

namespace Axon.Core.Authentication;

/// <summary>
/// Extension methods for configuring Axon JWT authentication services.
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Axon JWT authentication services to the service collection with default configuration.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The configuration instance.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddAxonAuthentication(this IServiceCollection services, IConfiguration configuration)
    {
        return services.AddAxonAuthentication(configuration, null);
    }

    /// <summary>
    /// Adds Axon JWT authentication services to the service collection with custom configuration.
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configuration">The configuration instance.</param>
    /// <param name="configureOptions">Optional action to configure JWT authentication options.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddAxonAuthentication(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<JwtAuthenticationOptions>? configureOptions)
    {
        // Configure JWT authentication options
        var optionsSection = configuration.GetSection(JwtAuthenticationOptions.SECTION_NAME);
        services.Configure<JwtAuthenticationOptions>(optionsSection);

        // Apply custom configuration if provided
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }

        // Validate configuration
        services.AddSingleton<IValidateOptions<JwtAuthenticationOptions>, JwtAuthenticationOptionsValidator>();

        // Register HTTP client for JWT key service
        services.AddHttpClient<IJwtKeyService, JwtKeyService>();

        // Register JWT key service
        services.AddSingleton<IJwtKeyService, JwtKeyService>();

        // Register JWT Bearer options setup
        services.AddSingleton<IConfigureOptions<JwtBearerOptions>, JwtBearerOptionsSetup>();

        // Configure authentication
        services.AddAuthentication(JwtBearerAuthenticationDefaults.AuthenticationScheme)
            .AddJwtBearer(JwtBearerAuthenticationDefaults.AuthenticationScheme);

        return services;
    }

    /// <summary>
    /// Adds Axon JWT authentication services with environment variable configuration.
    /// This method reads configuration from environment variables with the prefix "AXON_AUTH_".
    /// </summary>
    /// <param name="services">The service collection to add services to.</param>
    /// <param name="configureOptions">Optional action to configure JWT authentication options.</param>
    /// <returns>The service collection for chaining.</returns>
    public static IServiceCollection AddAxonAuthenticationFromEnvironment(
        this IServiceCollection services,
        Action<JwtAuthenticationOptions>? configureOptions = null)
    {
        // Configure options from environment variables
        services.Configure<JwtAuthenticationOptions>(options =>
        {
            var issuer = Environment.GetEnvironmentVariable("AXON_AUTH_ISSUER");
            if (!string.IsNullOrEmpty(issuer))
                options.Issuer = issuer;

            var audience = Environment.GetEnvironmentVariable("AXON_AUTH_AUDIENCE");
            if (!string.IsNullOrEmpty(audience))
                options.Audience = audience;

            var publicKeyUrl = Environment.GetEnvironmentVariable("AXON_AUTH_PUBLIC_KEY_URL");
            if (!string.IsNullOrEmpty(publicKeyUrl))
                options.PublicKeyUrl = publicKeyUrl;

            var validateIssuer = Environment.GetEnvironmentVariable("AXON_AUTH_VALIDATE_ISSUER");
            if (bool.TryParse(validateIssuer, out var validateIssuerValue))
                options.ValidateIssuer = validateIssuerValue;

            var validateAudience = Environment.GetEnvironmentVariable("AXON_AUTH_VALIDATE_AUDIENCE");
            if (bool.TryParse(validateAudience, out var validateAudienceValue))
                options.ValidateAudience = validateAudienceValue;

            var validateLifetime = Environment.GetEnvironmentVariable("AXON_AUTH_VALIDATE_LIFETIME");
            if (bool.TryParse(validateLifetime, out var validateLifetimeValue))
                options.ValidateLifetime = validateLifetimeValue;

            var clockSkew = Environment.GetEnvironmentVariable("AXON_AUTH_CLOCK_SKEW_MINUTES");
            if (int.TryParse(clockSkew, out var clockSkewMinutes))
                options.ClockSkew = TimeSpan.FromMinutes(clockSkewMinutes);
        });

        // Apply custom configuration if provided
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }

        // Register remaining services
        services.AddSingleton<IValidateOptions<JwtAuthenticationOptions>, JwtAuthenticationOptionsValidator>();
        services.AddHttpClient<IJwtKeyService, JwtKeyService>();
        services.AddSingleton<IJwtKeyService, JwtKeyService>();

        // Register JWT Bearer options setup
        services.AddSingleton<IConfigureOptions<JwtBearerOptions>, JwtBearerOptionsSetup>();

        // Configure authentication
        services.AddAuthentication(JwtBearerAuthenticationDefaults.AuthenticationScheme)
            .AddJwtBearer(JwtBearerAuthenticationDefaults.AuthenticationScheme);

        return services;
    }


}