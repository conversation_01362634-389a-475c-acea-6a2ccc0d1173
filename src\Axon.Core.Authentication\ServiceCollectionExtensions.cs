﻿using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Events;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Text;

namespace Axon.Core.Authentication;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAxonAuthentication(
        this IServiceCollection services,
        IConfiguration configuration,
        Action<JwtAuthenticationOptions>? configureOptions)
    {
        // fetch public key from cache/Axon endpoint?
        var rsaKey = string.Empty;

        var optionsSection = configuration.GetSection(JwtAuthenticationOptions.SECTION_NAME);
        services.Configure<JwtAuthenticationOptions>(optionsSection);

        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }

        services.AddSingleton<IValidateOptions<JwtAuthenticationOptions>, JwtAuthenticationOptionsValidator>();
        services.AddSingleton<AxonJwtBearerEvents>();
        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
            .AddJwtBearer(options =>
            {
                var serviceProvider = services.BuildServiceProvider();
                var jwtOptions = serviceProvider.GetRequiredService<IOptions<JwtAuthenticationOptions>>().Value;
                var events = serviceProvider.GetRequiredService<AxonJwtBearerEvents>();

                options.Events = events;
                options.SaveToken = true;
                options.RequireHttpsMetadata = true;

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuer = jwtOptions.ValidateIssuer,
                    ValidIssuer = jwtOptions.Issuer,
                    ValidateAudience = jwtOptions.ValidateAudience,
                    ValidAudience = jwtOptions.Audience,
                    ValidateLifetime = jwtOptions.ValidateLifetime,
                    ValidateIssuerSigningKey = jwtOptions.ValidateIssuerSigningKey,
                    IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(rsaKey)),
                    ClockSkew = jwtOptions.ClockSkew
                };
            });

        return services;
    }
}