using Axon.Core.Authentication.Configuration;
using Axon.Core.Authentication.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using NSubstitute;
using Shouldly;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Axon.Core.Authentication.Tests.Services;

public class MockHttpMessageHandler : HttpMessageHandler
{
    public HttpResponseMessage? Response { get; set; }
    public int CallCount { get; private set; }

    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        CallCount++;
        return Task.FromResult(Response ?? new HttpResponseMessage(HttpStatusCode.OK));
    }
}

public class JwtKeyServiceTests : IDisposable
{
    private readonly IOptions<JwtAuthenticationOptions> options;
    private readonly HttpClient httpClient;
    private readonly ILogger<JwtKeyService> logger;
    private readonly JwtKeyService jwtKeyService;
    private readonly MockHttpMessageHandler httpMessageHandler;

    public JwtKeyServiceTests()
    {
        var jwtOptions = new JwtAuthenticationOptions
        {
            PublicKeyUrl = "https://auth.example.com/jwks.json",
            PublicKeyCacheDuration = TimeSpan.FromMinutes(1),
            HttpTimeout = TimeSpan.FromSeconds(30)
        };

        options = Substitute.For<IOptions<JwtAuthenticationOptions>>();
        options.Value.Returns(jwtOptions);

        httpMessageHandler = new MockHttpMessageHandler();
        httpClient = new HttpClient(httpMessageHandler);
        logger = Substitute.For<ILogger<JwtKeyService>>();

        jwtKeyService = new JwtKeyService(options, httpClient, logger);
    }

    [Fact]
    public async Task GetSigningKeysAsync_WithValidJwks_ShouldReturnKeys()
    {
        // Arrange
        var jwks = new JsonWebKeySet();
        jwks.Keys.Add(new JsonWebKey
        {
            Kty = "RSA",
            Kid = "test-key-id",
            Use = "sig",
            N = "test-modulus",
            E = "AQAB"
        });

        var jwksJson = JsonSerializer.Serialize(jwks);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jwksJson, Encoding.UTF8, "application/json")
        };

        httpMessageHandler.Response = httpResponse;

        // Act
        var keys = await jwtKeyService.GetSigningKeysAsync();

        // Assert
        keys.ShouldNotBeNull();
        keys.Count().ShouldBe(1);
        keys.First().ShouldBeOfType<JsonWebKey>();
    }

    [Fact]
    public async Task GetSigningKeysAsync_WithCachedKeys_ShouldReturnCachedKeys()
    {
        // Arrange
        var jwks = new JsonWebKeySet();
        jwks.Keys.Add(new JsonWebKey { Kty = "RSA", Kid = "test-key-id" });
        var jwksJson = JsonSerializer.Serialize(jwks);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jwksJson, Encoding.UTF8, "application/json")
        };

        httpMessageHandler.Response = httpResponse;

        // Act - First call should fetch from HTTP
        var keys1 = await jwtKeyService.GetSigningKeysAsync();

        // Act - Second call should return cached keys
        var keys2 = await jwtKeyService.GetSigningKeysAsync();

        // Assert
        keys1.ShouldBeSameAs(keys2);

        // Verify HTTP was called only once
        httpMessageHandler.CallCount.ShouldBe(1);
    }

    [Fact]
    public async Task GetSigningKeysAsync_WithExpiredCache_ShouldRefetchKeys()
    {
        // Arrange - Set very short cache duration
        var shortCacheOptions = new JwtAuthenticationOptions
        {
            PublicKeyUrl = "https://auth.example.com/jwks.json",
            PublicKeyCacheDuration = TimeSpan.FromMilliseconds(10),
            HttpTimeout = TimeSpan.FromSeconds(30)
        };

        var shortCacheOptionsWrapper = Substitute.For<IOptions<JwtAuthenticationOptions>>();
        shortCacheOptionsWrapper.Value.Returns(shortCacheOptions);

        // Create a new service instance with short cache duration
        var testHttpMessageHandler = new MockHttpMessageHandler();
        var testHttpClient = new HttpClient(testHttpMessageHandler);
        var testJwtKeyService = new JwtKeyService(shortCacheOptionsWrapper, testHttpClient, logger);

        var jwks = new JsonWebKeySet();
        jwks.Keys.Add(new JsonWebKey { Kty = "RSA", Kid = "test-key-id" });
        var jwksJson = JsonSerializer.Serialize(jwks);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jwksJson, Encoding.UTF8, "application/json")
        };

        testHttpMessageHandler.Response = httpResponse;

        // Act - First call
        await testJwtKeyService.GetSigningKeysAsync();

        // Wait for cache to expire
        await Task.Delay(50);

        // Act - Second call after cache expiry
        await testJwtKeyService.GetSigningKeysAsync();

        // Assert - HTTP should be called twice
        testHttpMessageHandler.CallCount.ShouldBe(2);

        // Cleanup
        testJwtKeyService.Dispose();
        testHttpClient.Dispose();
    }

    [Fact]
    public async Task GetSigningKeysAsync_WithHttpError_ShouldThrowException()
    {
        // Arrange
        var httpResponse = new HttpResponseMessage(HttpStatusCode.InternalServerError);
        httpMessageHandler.Response = httpResponse;

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            () => jwtKeyService.GetSigningKeysAsync());

        exception.Message.ShouldContain("Failed to retrieve JWT signing keys");
    }

    [Fact]
    public async Task GetSigningKeysAsync_WithEmptyJwks_ShouldThrowException()
    {
        // Arrange
        var emptyJwks = new JsonWebKeySet(); // No keys
        var jwksJson = JsonSerializer.Serialize(emptyJwks);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jwksJson, Encoding.UTF8, "application/json")
        };

        httpMessageHandler.Response = httpResponse;

        // Act & Assert
        var exception = await Should.ThrowAsync<InvalidOperationException>(
            () => jwtKeyService.GetSigningKeysAsync());

        exception.Message.ShouldContain("No keys found in JWKS response");
    }

    [Fact]
    public async Task ClearCacheAsync_ShouldClearCachedKeys()
    {
        // Arrange
        var jwks = new JsonWebKeySet();
        jwks.Keys.Add(new JsonWebKey { Kty = "RSA", Kid = "test-key-id" });
        var jwksJson = JsonSerializer.Serialize(jwks);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(jwksJson, Encoding.UTF8, "application/json")
        };

        httpMessageHandler.Response = httpResponse;

        // Act - Fetch keys to populate cache
        await jwtKeyService.GetSigningKeysAsync();

        // Clear cache
        await jwtKeyService.ClearCacheAsync();

        // Fetch keys again
        await jwtKeyService.GetSigningKeysAsync();

        // Assert - HTTP should be called twice (once before clear, once after)
        httpMessageHandler.CallCount.ShouldBe(2);
    }

    [Fact]
    public void Constructor_WithNullOptions_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Should.Throw<ArgumentNullException>(() => 
            new JwtKeyService(null!, httpClient, logger));
    }

    [Fact]
    public void Constructor_WithNullHttpClient_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Should.Throw<ArgumentNullException>(() => 
            new JwtKeyService(options, null!, logger));
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        Should.Throw<ArgumentNullException>(() => 
            new JwtKeyService(options, httpClient, null!));
    }

    public void Dispose()
    {
        jwtKeyService?.Dispose();
        httpClient?.Dispose();
    }
}
